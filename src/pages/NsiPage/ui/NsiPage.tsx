import { tabsNsi } from 'entities/pages/nsiPage.entities.ts'
import { observer } from 'mobx-react'
import { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { classNames } from 'shared/lib/classNames/classNames'
import { locationParse } from 'shared/lib/locationParse'
import { useStore } from 'stores/useStore'
import { Tabs } from 'widgets/Tabs'

import cls from './NsiPage.module.scss'
import { Indicators } from './ui/Indicators'
import { ListOfCascades } from './ui/ListOfCascades'
import { ListOfObjects } from './ui/ListOfObjects/ListOfObjects'
import { ListOfRestrictions } from './ui/ListOfRestrictions/ListOfRestrictions'
import { ListOfTerritories } from './ui/ListOfTerritories/ListOfTerritories'
import { ReferenceData } from './ui/ReferenceData/ReferenceData'

interface NsiPageProps {
  className?: string
}

const NsiPage = observer((props: NsiPageProps) => {
  const { className } = props
  const { authStore } = useStore()
  const { userDetail } = authStore
  const history = useNavigate()

  const { path = 'listofobjects' } = locationParse(location.search)

  useEffect(() => {
    return () => {
      localStorage.removeItem('list-of-objects')
    }
  }, [])

  const bodyRef = useRef<HTMLDivElement>(null)
  const [height, setHeight] = useState<number | null>(null)

  const changeHeightTable = () => {
    const el = bodyRef.current

    if (el) {
      const computedStyle = getComputedStyle(el)
      const paddingTop = parseInt(computedStyle.paddingTop) || 0
      const paddingBottom = parseInt(computedStyle.paddingBottom) || 0
      const borderTop = parseInt(computedStyle.borderTopWidth) || 0
      const borderBottom = parseInt(computedStyle.borderBottomWidth) || 0

      const curHeight = el.clientHeight - paddingTop - paddingBottom - borderTop - borderBottom
      setHeight(curHeight)
    }
  }

  useEffect(() => {
    window.addEventListener('resize', changeHeightTable)

    return () => {
      window.removeEventListener('resize', changeHeightTable)
      localStorage.removeItem(`expanded-list-of-objects`)
    }
  }, [])

  useEffect(() => {
    changeHeightTable()
  }, [bodyRef])

  return (
    <div className={classNames(cls.NsiPage, {}, className ? [className] : [])}>
      <div className={classNames(cls.Header, {}, [])}>
        <Tabs
          items={tabsNsi.filter((el) => el.isView)}
          selectedValue={path}
          onChange={(value) => {
            history(`?path=${value}`)
          }}
        />
      </div>
      <div ref={bodyRef} className={classNames(cls.Body, {}, [])}>
        {path === 'listofobjects' && <ListOfObjects height={height ?? 500} userDetail={userDetail} />}
        {path === 'listofterritories' && <ListOfTerritories userDetail={userDetail} />}
        {path === 'listofrestrictions' && <ListOfRestrictions userDetail={userDetail} />}
        {path === 'listofcascades' && <ListOfCascades height={height ?? 500} />}
        {path === 'referencedata' && <ReferenceData height={height ?? 500} />}
        {path === 'indicators' && <Indicators height={height ?? 500} />}
      </div>
    </div>
  )
})

export default NsiPage

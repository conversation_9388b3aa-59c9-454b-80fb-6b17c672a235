import { I<PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@mui/material'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import s from 'pages/NsiPage/ui/ui/ListOfTerritories/ListOfTerritories.module.scss'
import { FC, Fragment, useEffect, useMemo, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { AccessControl } from 'shared/ui/AccessControl'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { Switch } from 'shared/ui/Switch'
import { ICascades } from 'stores/NsiStore/ListOfCascadesStore.ts'
import { useStore } from 'stores/useStore.ts'
import { IColumn, Table } from 'widgets/Table'

import cls from './ListOfCascades.module.scss'
import { ModalAddCascade } from './ui/ModalAddCascade'

export interface IListOfCascades {
  height: number
}

const HEADER_HEIGHT = 50

export const ListOfCascades: FC<IListOfCascades> = observer((props) => {
  const { height } = props
  const { listOfCascadesStore } = useStore()
  const { cascades, editMode, originCascades, isLoading } = listOfCascadesStore
  const [activeOnly, setActiveOnly] = useState<boolean>(true)
  const [showArchived, setShowArchived] = useState<boolean>(false)
  const [isAddModal, setIsAddModal] = useState(false)
  const hasErrorName = cascades.some((el) => el.errorName)

  const bodyHeight = useMemo(() => height - HEADER_HEIGHT, [height])

  useHotkeys('ctrl+shift+s', () => editMode && listOfCascadesStore.saveData(showArchived, activeOnly))
  useHotkeys('ctrl+shift+x', () => editMode && listOfCascadesStore.resetData())

  useEffect(() => {
    listOfCascadesStore.getCascades(showArchived, activeOnly)

    return () => {
      listOfCascadesStore.resetLoading()
    }
  }, [activeOnly, showArchived])

  const columns: IColumn[] = [
    {
      name: 'name',
      title: 'Название',
      width: 450,
      editingEnabled: true,
      render: (value: string, row: ICascades) => {
        const errorText = () => {
          if (row.errorName) {
            return `Каскад с таким названием уже существует`
          }

          return null
        }
        const warningText = () => {
          const isPlants = row.plants.length > 1
          if (isPlants) {
            return `Ошибка формирования каскада: присутствуют несвязанные станции`
          }

          return null
        }
        const error = errorText()
        const warning = warningText()

        return (
          <>
            {error && (
              <Tooltip title={error}>
                <div className={cls.ErrorTooltip}>
                  <Icon name='information' width={13} />
                </div>
              </Tooltip>
            )}
            {warning && (
              <Tooltip title={warning}>
                <div className={cls.WarningTooltip}>
                  <Icon name='information' width={13} />
                </div>
              </Tooltip>
            )}
            <>{value}</>
          </>
        )
      },
      editableMaxLength: 150,
    },
    {
      name: 'plants',
      title: 'Состав',
      width: 600,
      editingEnabled: false,
      isBlockedSorting: true,
      customSearching: (value: { name: string; active: boolean }[][], filter) => {
        if (value.some((el) => el.some((item) => item.name.toLowerCase().includes(filter.value?.toLowerCase())))) {
          return true
        }

        return false
      },
      render: (value: { name: string; active: boolean }[][], row: ICascades) => {
        return (
          <div className={cls.Plants}>
            {value.map((group) => {
              return (
                <div key={row.id + JSON.stringify(group)} className={cls.Group}>
                  {group.map((el) => {
                    return (
                      <div
                        key={el.name}
                        className={classNames(
                          cls.Plant,
                          {
                            [cls.NotActiveStation]: !el.active,
                            [cls.ActiveStation]: el.active,
                          },
                          [],
                        )}
                      >
                        {el.name}
                      </div>
                    )
                  })}
                </div>
              )
            })}
          </div>
        )
      },
    },
    {
      name: 'actions',
      title: '',
      width: 50,
      editingEnabled: false,
      headRender: () => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_NSI]}>
            <div className={s.ActionHeader}>
              <Tooltip title='Создать каскад'>
                <IconButton
                  sx={{
                    color: 'var(--primary-color)',
                  }}
                  onClick={() => {
                    setIsAddModal(true)
                  }}
                >
                  <Icon className={s.AddIcon} name='plus' width={13} />
                </IconButton>
              </Tooltip>
            </div>
          </AccessControl>
        )
      },
      render: (_: unknown, row: ICascades) => {
        return (
          <AccessControl rules={[ROLES.TECH_ADMIN_NSI]}>
            <div className={cls.Actions}>
              {row.allowDelete && (
                <Tooltip title='Удалить'>
                  <IconButton
                    onClick={() => {
                      listOfCascadesStore.deleteCascade(row.tabId)
                    }}
                    className={cls.iconRedBtn}
                  >
                    <Icon name='trash' width={13} height={13} />
                  </IconButton>
                </Tooltip>
              )}
              {(row.allowArchive || row.archived) && (
                <Tooltip title={row.archived ? 'Добавить из архива' : 'Убрать в архив'}>
                  <IconButton
                    onClick={() => {
                      listOfCascadesStore.editArchive(row.tabId)
                    }}
                    className={classNames(row.archived ? cls.iconGreenBtn : cls.iconRedBtn)}
                  >
                    <Icon name={row.archived ? 'archivePlus' : 'archiveMinus'} width={13} height={13} />
                  </IconButton>
                </Tooltip>
              )}
            </div>
          </AccessControl>
        )
      },
    },
  ]

  const handleRowsChange = (rows: ICascades[]): void => {
    const trimmedRows = rows.map((row) => ({
      ...row,
      name: row.name.trim(),
    }))
    listOfCascadesStore.editName(trimmedRows)
  }

  return (
    <CheckEditComponent isEdit={editMode}>
      <div className={cls.Container}>
        <SubtitleWithActions
          title='Перечень каскадов'
          actions={[
            <Fragment key='actions'>
              <Switch
                label='Только действующие'
                checked={activeOnly}
                onChange={(_, value) => {
                  setActiveOnly && setActiveOnly(value)
                }}
                disabled={editMode}
              />
              <Switch
                label='Показ архивных'
                checked={showArchived}
                onChange={(_, value) => {
                  setShowArchived && setShowArchived(value)
                }}
                disabled={editMode}
              />
              <Button
                className={classNames(cls.Action, {}, [])}
                variant='outlined'
                onClick={() => {
                  listOfCascadesStore.resetData()
                }}
                disabled={!editMode}
              >
                Сбросить
              </Button>
              <Button
                className={classNames(cls.Action, {}, [])}
                onClick={() => listOfCascadesStore.saveData(showArchived, activeOnly)}
                disabled={!editMode || hasErrorName}
              >
                Сохранить
              </Button>
            </Fragment>,
          ]}
          isActionsVisible
        />
        <div className={cls.Table}>
          <Table
            rows={cascades}
            columns={columns}
            height={bodyHeight}
            initialData={originCascades ?? []}
            setRows={handleRowsChange}
            editMode
            columnSearchDisabled={['actions']}
            loading={isLoading}
            showSearchControls
            showSortingControls
          />
        </div>
      </div>
      {isAddModal && (
        <ModalAddCascade
          rows={cascades}
          onConfirm={(name) => {
            listOfCascadesStore.addCascade(name)
            setIsAddModal(false)
          }}
          onClose={() => setIsAddModal(false)}
        />
      )}
    </CheckEditComponent>
  )
})

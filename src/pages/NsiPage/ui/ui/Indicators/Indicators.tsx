import { Icon<PERSON>utton } from '@mui/material'
import { TIndicatorChild, TIndicatorItem } from 'entities/pages/nsiPage.entities'
import { ROLES } from 'entities/shared/roles.entities'
import { observer } from 'mobx-react'
import { FC, Fragment, useEffect, useMemo, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { Button } from 'shared/ui/Button'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { Icon } from 'shared/ui/Icon'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore'
import { IColumn, Table } from 'widgets/Table'

import cls from './Indicators.module.scss'
import { AddEditIndicatorModal } from './ui/AddEditIndicatorModal'

interface Props {
  height: number
}

const HEADER_HEIGHT = 50
const columnSearchDisabled = ['code', 'fullName', 'shortName', 'measurementUnit', 'precision', 'comment', 'action']

export const Indicators: FC<Props> = observer(({ height }) => {
  const {
    indicatorStore,
    authStore: {
      userDetail: { roles },
    },
  } = useStore()
  const {
    updateIndicator,
    setSelectedIndicator,
    getIndicators,
    resetStore,
    saveChanges,
    resetChanges,
    isDataModified,
    isLoading,
  } = indicatorStore
  const [expandedRowIds, setExpandedRowIds] = useState<string[]>([])
  const editMode = roles.some((el) => el.role === ROLES.TECH_ADMIN_NSI)

  const bodyHeight = useMemo(() => height - HEADER_HEIGHT, [height])

  const onCloseModal = () => {
    setSelectedIndicator(null)
  }

  const onSubmit = (values: TIndicatorChild) => {
    updateIndicator(values)
    onCloseModal()
  }

  const columns: IColumn[] = [
    {
      name: 'code',
      title: 'Код',
      width: 70,
      render: (value, row: TIndicatorItem) => (row?.children ? <></> : value),
    },
    {
      name: 'fullName',
      title: 'Полное наименование',
      width: 600,
      render: (value, row: TIndicatorItem) => (
        <div className={classNames(cls.tableCell, { [cls.tableCellTextBold]: !!row?.children })}>{value}</div>
      ),
    },
    {
      name: 'shortName',
      title: 'Краткое наименование',
      width: 180,
      render: (value) => <div className={cls.tableCell}>{value}</div>,
    },
    {
      name: 'measurementUnit',
      title: 'Ед.изм',
      width: 70,
    },
    {
      name: 'precision',
      title: 'Точность',
      width: 80,
    },
    {
      name: 'comment',
      title: 'Комментарий',
      width: 600,
      render: (value) => <div className={cls.tableCell}>{value}</div>,
    },
    {
      name: 'action',
      title: '',
      width: 38,
      render: (_, row: TIndicatorChild | TIndicatorItem) => {
        if ('children' in row) return null

        return (
          <IconButton
            onClick={() => setSelectedIndicator(row)}
            className={classNames(cls.iconBtnSetting, { [cls.iconBtnSettingActive]: editMode })}
            disabled={!editMode}
          >
            <Icon name='settings' width={13} />
          </IconButton>
        )
      },
    },
  ]

  useEffect(() => {
    getIndicators().then(() => {
      const defaultExpandedRowIds = indicatorStore.indicators.flatMap((item) => [
        item.tabId,
        ...item.children.map((el) => el.tabId),
      ])
      setExpandedRowIds(defaultExpandedRowIds)
    })

    return () => resetStore()
  }, [])

  return (
    <CheckEditComponent isEdit={isDataModified}>
      <div className={cls.container} style={{ height: bodyHeight }}>
        <SubtitleWithActions
          title='Показатели'
          actions={[
            <Fragment key='actions'>
              <Button variant='outlined' onClick={resetChanges} disabled={!editMode || !isDataModified}>
                Сбросить
              </Button>

              <Button onClick={saveChanges} disabled={!editMode || !isDataModified}>
                Сохранить
              </Button>
            </Fragment>,
          ]}
          isActionsVisible
        />

        <Table
          childKey='code'
          loading={isLoading}
          rows={indicatorStore.indicators}
          columns={columns}
          tableType='nsi'
          height={bodyHeight}
          expandedRowIds={expandedRowIds}
          setExpandenRowIds={(e: string[]) => setExpandedRowIds(e)}
          columnSearchDisabled={columnSearchDisabled}
          showSortingControls={false}
        />
      </div>

      {indicatorStore.selectedIndicator && (
        <AddEditIndicatorModal onSubmit={onSubmit} row={indicatorStore.selectedIndicator} onClose={onCloseModal} />
      )}
    </CheckEditComponent>
  )
})

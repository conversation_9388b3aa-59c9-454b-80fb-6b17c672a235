.container {
  padding: 0.2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  padding: 5px;
  border-radius: 8px;
  min-height: 40px;
  background: var(--background-color-primary);
  color: var(--text-color);
  display: flex;
  justify-content: space-between;

  &Content {
    display: flex;
    align-items: center;
    gap: 1.2rem;

    &Title {
      margin-right: 2rem;
    }

    &Calendar {
      display: flex;
      gap: 0.5rem;
      &Changes {
        color: var(--text-gray);
        font-size: 0.75rem;
        width: 140px;
        white-space: nowrap;
      }
    }

    &Buttons {
      display: flex;
      gap: 0.4rem;
    }
  }

  &Actions {
    display: flex;
    align-items: center;
    justify-content: center;

    &LoadingButton {
      width: 82px;
      height: 26px;
    }
  }
}

.DatePicker {
  width: 125px;
  height: 24px;
  border-radius: 4px;

  & > div {
    height: 26px;
  }

  &Wrapper {
    width: 220px;
  }
}

.table {
  &Empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-gray);
  }

  &Cell {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden !important;
    display: inline-block;
    max-width: 241px;

    &HasPrevChanges {
      font-weight: 700;
      color: var(--green-color) !important;
    }

    &HasTodayChanges {
      background-color: rgba(#0263d9, 0.1);
    }
  }
}

.iconNextVersion {
  transform: rotate(180deg);
}

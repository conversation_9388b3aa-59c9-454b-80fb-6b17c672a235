import { Tooltip } from '@mui/material'
import { format } from 'date-fns'
import { IReferenceDataCell } from 'entities/pages/nsiPage.entities'
import { ROLES } from 'entities/shared/roles.entities'
import { observer } from 'mobx-react'
import { FC, useEffect, useMemo, useRef } from 'react'
import { classNames } from 'shared/lib/classNames'
import { AccessControl } from 'shared/ui/AccessControl'
import { Button } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { Icon } from 'shared/ui/Icon'
import { Loader } from 'shared/ui/Loader'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore'
import { IColumn, Table } from 'widgets/Table'

import cls from './ReferenceData.module.scss'

/** Ширины колонок. Наименования колонок получаем с бэка */
const COLS_WIDTH = [250, 150, 70, 70, 70, 70, 70, 120, 120, 145, 150, 105, 100, 130, 150]
const columnsWithSearching = new Set(['0', '1', '10']) // Индексы колонок, в которых есть поиск
const columnSearchDisabled = Array.from({ length: COLS_WIDTH.length }, (_, i) => i.toString()).filter(
  (i) => !columnsWithSearching.has(i),
)
const HEADER_HEIGHT = 50

interface Props {
  height: number
}

export const ReferenceData: FC<Props> = observer(({ height }) => {
  const { referenceDataStore } = useStore()
  const controllerRef = useRef<null | AbortController>(null)
  const bodyHeight = useMemo(() => height - HEADER_HEIGHT, [height])

  const columns: IColumn[] = useMemo(
    () =>
      COLS_WIDTH.map((width, index) => {
        let title = ''
        // если данные загрузились - достаем title по индексу
        const colTitle = referenceDataStore.referenceData?.table?.columns?.[index]?.title
        if (colTitle) {
          // парсим мнемоники в заголовках (например &sup3;)
          const textarea = document.createElement('textarea')
          textarea.innerHTML = colTitle
          title = textarea.value
        }

        return {
          name: index.toString(),
          title,
          width,
          isOverflowVisible: true,
          render: (value: IReferenceDataCell) => <span className={cls.tableCell}>{value?.value ?? '-'}</span>,
          customSearching: (value: { value: string }, filter) =>
            value?.value?.toUpperCase().includes(filter?.value?.toUpperCase()),
          customSorting: (a: IReferenceDataCell, b: IReferenceDataCell) => {
            const valA = a?.value
            const valB = b?.value

            // Если оба значения числа — сравниваем как числа
            if (!isNaN(Number(valA)) && !isNaN(Number(valB))) {
              return Number(valA) - Number(valB)
            }

            // Иначе сравниваем как строки
            const textA = (valA ?? '').toString().toUpperCase()
            const textB = (valB ?? '').toString().toUpperCase()

            if (textA < textB) {
              return -1
            }
            if (textA > textB) {
              return 1
            }

            return 0
          },
          getCellClassName: (cellValue) => {
            const value = cellValue as IReferenceDataCell

            return classNames('', {
              [cls.tableCellHasPrevChanges]: value?.previousChanged,
              [cls.tableCellHasTodayChanges]: value?.todayChanged,
            })
          },
          getCellRenderValue: (value: IReferenceDataCell) => value?.value?.toString(),
        }
      }),
    [referenceDataStore.referenceData],
  )

  const handleChangeDate = async (date: Date) => {
    try {
      if (controllerRef.current) {
        controllerRef.current.abort()
        controllerRef.current = null
      }
      controllerRef.current = new AbortController()
      referenceDataStore.handleChangeDate(date)
      await referenceDataStore.getReferenceData(date, controllerRef.current.signal)
    } catch (error) {
      console.log(error)
    } finally {
      controllerRef.current = null
    }
  }

  const handleChangeVersion = (direction: 'prev' | 'next') => {
    let date: Date | null = null
    if (referenceDataStore.referenceData) {
      if (direction === 'prev' && referenceDataStore.referenceData.previousDate) {
        date = new Date(referenceDataStore.referenceData.previousDate)
      } else if (direction === 'next' && referenceDataStore.referenceData.nextDate) {
        date = new Date(referenceDataStore.referenceData.nextDate)
      }

      if (date) {
        handleChangeDate(date)
      }
    }
  }

  const handleToggleActiveOnly = (_: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    referenceDataStore.setShowActiveOnly(checked)
  }

  useEffect(() => {
    referenceDataStore.getLastTaskInfoReferenceData(true)
    controllerRef.current = new AbortController()
    referenceDataStore.getReferenceData(referenceDataStore.selectedDate, controllerRef.current.signal).finally(() => {
      controllerRef.current = null
    })

    return () => {
      referenceDataStore.resetStore()
    }
  }, [])

  return (
    <div className={cls.container}>
      <div className={cls.header}>
        <div className={cls.headerContent}>
          <h2 className={cls.headerContentTitle}>Справочные данные</h2>

          <div className={cls.headerContentCalendar}>
            <div className={cls.DatePickerWrapper}>
              <DatePicker
                isDayOfWeek
                isArrow
                className={classNames(cls.DatePicker)}
                value={referenceDataStore.selectedDate}
                setValue={handleChangeDate}
              />
            </div>

            <div className={cls.headerContentCalendarChanges}>
              {referenceDataStore.referenceData?.currentDate && (
                <span>изменения от {format(new Date(referenceDataStore.referenceData.currentDate), 'dd.MM.yyyy')}</span>
              )}
            </div>
          </div>

          <div className={cls.headerContentButtons}>
            <Button
              size='small'
              disabled={referenceDataStore.disablePrevButton}
              message={referenceDataStore.disablePrevButton ? 'Предыдущая версия отсутствует' : null}
              variant='outlined'
              onClick={() => handleChangeVersion('prev')}
            >
              <Icon width={12} name='arrowLeft' />
              Предыдущая версия
            </Button>

            <Button
              size='small'
              disabled={referenceDataStore.disableNextButton}
              message={referenceDataStore.disableNextButton ? 'Следующая версия отсутствует' : null}
              variant='outlined'
              onClick={() => handleChangeVersion('next')}
            >
              Следующая версия
              <Icon width={12} className={cls.iconNextVersion} name='arrowLeft' />
            </Button>
          </div>
        </div>

        <div className={cls.headerActions}>
          <Switch
            label='Только действующие'
            checked={referenceDataStore.showActiveOnly}
            onChange={handleToggleActiveOnly}
          />

          <AccessControl rules={[ROLES.TECH_ADMIN_NSI]}>
            <Tooltip title='Загрузить данные из ОИК СК-11'>
              <span>
                <LoadingButton
                  variant='contained'
                  onClick={referenceDataStore.loadReferenceDataSK11}
                  loading={referenceDataStore.isLoadingSk11InProgress}
                  className={cls.headerActionsLoadingButton}
                >
                  Загрузить
                </LoadingButton>
              </span>
            </Tooltip>
          </AccessControl>
        </div>
      </div>

      {(referenceDataStore.loading || referenceDataStore.referenceDataAbsent) && (
        <div style={{ height: bodyHeight }}>
          <div className={cls.tableEmpty}>
            {!referenceDataStore.loading && referenceDataStore.referenceDataAbsent && (
              <div>Справочные данные на указанную дату отсутствуют</div>
            )}

            {referenceDataStore.loading && <Loader />}
          </div>
        </div>
      )}

      <Table
        loading={referenceDataStore.loading}
        className={classNames(cls.table)}
        columns={columns}
        rows={referenceDataStore.referenceDataTableRows}
        height={bodyHeight}
        columnSearchDisabled={columnSearchDisabled}
      />
    </div>
  )
})

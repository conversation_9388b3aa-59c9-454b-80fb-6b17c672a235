import { I<PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from '@mui/material'
import { IWerDepartment } from 'entities/api/calcModelWerManager.entities'
import { observer } from 'mobx-react'
import { FC, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { prepareDate } from 'shared/lib/prepareData'
import { Icon } from 'shared/ui/Icon'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import { renderWerDepartments } from '../../lib/renderFuctions'
import { sortFormattedDates } from '../../lib/sortFormattedDates'
import { AddPlantModal } from '../ui/AddPlantModal'
import { UpDownStreamTableCell } from '../ui/UpDownStreamTableCell'
import cls from './DownstreamPlants.module.scss'

interface IDownstreamPlantsRow {
  tabId: number
  plantName: string
  travelTime: number | null
  departmentName: IWerDepartment[]
  startDate: string
  endDate: string
  affluent: boolean
}

interface Props {
  isEditingEnabled: boolean
}

export const DownstreamPlants: FC<Props> = observer(({ isEditingEnabled }) => {
  const { calcModelWerStore } = useStore()
  const {
    listOfStationsStore: {
      parametersStore: { plantCascades, updateAffluentValue, editMode, updatePlantDate, removePlant },
    },
  } = calcModelWerStore

  // Функция-костыль для синхронизации рендера таблицы с изменениями в сторе
  const mapPlantData = () =>
    plantCascades?.downstreamPlants?.map((plant) => ({
      tabId: plant.plantId,
      plantName: plant.plantName,
      travelTime: null, // Данные для времени добегания пока бек не дает
      departmentName: plant.werDepartments,
      startDate: prepareDate(plant.startDate),
      endDate: plant.endDate ? prepareDate(plant.endDate) : null,
      affluent: plant.affluent,
      type: 'ADDED', // Костыль, чтобы можно было редактировать startDate
    })) || []

  const initialRows = mapPlantData()

  const [rows, setRows] = useState(initialRows)
  const [isOpenAddNewPlantModal, setIsOpenAddNewPlantModal] = useState(false)

  useEffect(() => {
    setRows(initialRows)
  }, [plantCascades?.downstreamPlants])

  const baseColumns = [
    {
      name: 'plantName',
      title: 'Станция',
      width: 200,
      editingEnabled: false,
      render: (value: string) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'travelTime',
      title: 'tдоб, дни',
      width: 100,
      editingEnabled: false,
      render: (value: string) => <UpDownStreamTableCell value={value} />,
    },
    {
      name: 'departmentName',
      title: 'ДЦ ВЭР',
      width: 180,
      editingEnabled: false,
      render: renderWerDepartments,
    },
    {
      name: 'startDate',
      title: 'Дата начала связи',
      width: 155,
      editingEnabled: true,
      editType: 'date',
      onAfterChange: (val: IDownstreamPlantsRow['startDate'], row: IDownstreamPlantsRow) => {
        updatePlantDate('downstreamPlants', row.tabId, 'startDate', val)
      },
      render: (value: string) => <UpDownStreamTableCell value={value} />,
      customSorting: sortFormattedDates,
    },
    {
      name: 'endDate',
      title: 'Дата окончания связи',
      width: 180,
      editingEnabled: true,
      editType: 'date',
      canClearCell: true,
      onAfterChange: (val: IDownstreamPlantsRow['endDate'], row: IDownstreamPlantsRow) => {
        updatePlantDate('downstreamPlants', row.tabId, 'endDate', val)
      },
      render: (value: string) => <UpDownStreamTableCell value={value} />,
      customSorting: sortFormattedDates,
    },
    {
      name: 'affluent',
      title: 'Подпор',
      width: 83,
      render: (value: boolean, row: IDownstreamPlantsRow) => {
        return (
          <div className={cls.switch}>
            <Switch
              disabled={!isEditingEnabled}
              checked={value}
              onChange={(e) => {
                updateAffluentValue(row.tabId, e.target.checked)
                setRows(mapPlantData())
              }}
            />
          </div>
        )
      },
    },
  ]

  const actionColumn = {
    name: 'action',
    title: '',
    width: 50,
    editingEnabled: false,
    headRender: () => {
      return (
        <div className={cls.actionHeader}>
          <Tooltip title='Добавить связь'>
            <span>
              <IconButton
                sx={{
                  color: 'var(--primary-color)',
                  display: 'inline-flex!important',
                }}
                className={cls.addIcon}
                onClick={() => {
                  setIsOpenAddNewPlantModal(true)
                }}
                disabled={!isEditingEnabled}
              >
                <Icon name='plus' width={13} />
              </IconButton>
            </span>
          </Tooltip>
        </div>
      )
    },
    render: (_: unknown, row: IDownstreamPlantsRow) => {
      return (
        <div className={cls.actionsWrapper}>
          <div className={cls.iconCell}>
            <Tooltip title='Удалить'>
              <span>
                <IconButton
                  onClick={() => {
                    removePlant('downstreamPlants', row.tabId)
                    setRows(mapPlantData())
                  }}
                  className={classNames('', { [cls.iconRedBtn]: isEditingEnabled })}
                  disabled={!isEditingEnabled}
                >
                  <Icon name='trash' width={13} height={13} />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        </div>
      )
    },
  }

  const columns = editMode ? [...baseColumns, actionColumn] : baseColumns

  return (
    <div className={cls.container}>
      <div className={cls.title}>Нижележащая ГЭС</div>
      <Table
        initialData={initialRows}
        setRows={setRows}
        columns={columns}
        rows={rows}
        height={300}
        columnSearchDisabled={['plantName', 'travelTime', 'departmentName', 'startDate', 'endDate', 'affluent']}
        disabledDragAndDrop
        editMode={isEditingEnabled}
        className={cls.table}
      />
      {isOpenAddNewPlantModal && (
        <AddPlantModal onClose={() => setIsOpenAddNewPlantModal(false)} plantType='downstreamPlants' />
      )}
    </div>
  )
})

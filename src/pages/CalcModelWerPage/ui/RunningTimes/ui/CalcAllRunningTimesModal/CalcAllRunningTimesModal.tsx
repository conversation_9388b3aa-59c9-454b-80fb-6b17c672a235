import CloseIcon from '@mui/icons-material/Close'
import { IconButton } from '@mui/material'
import { format } from 'date-fns'
import { ICalcAllRunningTimesParams } from 'entities/api/calcModelWerManager.entities'
import { DATE_FORMATS } from 'entities/constants'
import { FC, useState } from 'react'
import { classNames } from 'shared/lib/classNames'
import { DataPickerValue } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'

import { initDateValues, TDateType } from '../../lib/constants'
import { CalcPeriodSettings } from '../CalcPeriodSettings'
import cls from './CalcAllRunningTimesModal.module.scss'

interface Props {
  onClose: () => void
  onSubmit: (data: ICalcAllRunningTimesParams) => Promise<void>
}

const validate = (dateRange: DataPickerValue) => {
  if (!dateRange[0] || !dateRange[1]) {
    return 'Расчетный период должен быть установлен для расчета времени добегания'
  }

  return ''
}

export const CalcAllRunningTimesModal: FC<Props> = ({ onClose, onSubmit }) => {
  const [dateRange, setDateRange] = useState<DataPickerValue>([null, null])
  const [checkboxValues, setCheckboxValues] = useState<Record<TDateType, boolean>>(() => ({
    days: false,
    months: false,
    years: false,
  }))
  const [dateValues, setDateValues] = useState<Record<TDateType, number[]>>(() => ({ ...initDateValues }))
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleChangeDate = (value: DataPickerValue) => {
    setDateRange(value)
    if (error) {
      const newError = validate(value)
      setError(newError)
    }
  }

  const onCheckboxChange = (key: TDateType, value: boolean) => {
    setCheckboxValues((prev) => ({ ...prev, [key]: value }))
  }

  const onChangeDateValues = (key: TDateType, values: number[]) => {
    setDateValues((prev) => ({ ...prev, [key]: values.sort((a, b) => Number(a) - Number(b)) }))
  }

  const onCalcClick = async () => {
    const newError = validate(dateRange)
    setError(newError)
    if (newError) return

    const [periodBegin, periodEnd] = dateRange
    if (periodBegin && periodEnd) {
      try {
        setIsLoading(true)
        await onSubmit({
          days: checkboxValues.days ? dateValues.days : [],
          months: checkboxValues.months ? dateValues.months : [],
          years: checkboxValues.years ? dateValues.years : [],
          periodBegin: format(periodBegin, DATE_FORMATS.yyyyMMdd),
          periodEnd: format(periodEnd, DATE_FORMATS.yyyyMMdd),
        })
      } catch (error) {
        console.log(error)
      } finally {
        setIsLoading(false)
      }
    }
  }

  return (
    <Modal
      open
      title={
        <div className={cls.titleRow}>
          <span>Расчёт времени добегания всех связей</span>
          <IconButton className={cls.titleRowButton} onClick={onClose} disabled={isLoading}>
            <CloseIcon className={classNames(cls.titleRowIcon, { [cls.titleRowIconActive]: !isLoading })} />
          </IconButton>
        </div>
      }
      className={cls.modal}
      maxWidth='lg'
      actions={[
        <div key='actions' className={cls.actions}>
          <LoadingButton variant='contained' className={cls.loadingButton} loading={isLoading} onClick={onCalcClick}>
            Рассчитать
          </LoadingButton>
        </div>,
      ]}
    >
      <div className={cls.modalContent}>
        <CalcPeriodSettings
          checkboxValues={checkboxValues}
          dateRange={dateRange}
          dateValues={dateValues}
          handleChangeDate={handleChangeDate}
          onCheckboxChange={onCheckboxChange}
          onChangeDateValues={onChangeDateValues}
          error={error}
        />
      </div>
    </Modal>
  )
}

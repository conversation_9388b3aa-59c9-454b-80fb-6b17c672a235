export * from './getCalculationSearchParams.ts'
export * from './validateDailyOutput'
export * from 'stores/CalculationsPageStore/lib/stationHeaderClassNameConfig.ts'

export const roundingTo3DigitsEminEmaxPgen = (num: number): string | number => {
  return Number.isInteger(num) ? num : parseFloat(num.toFixed(3))
}

export const getIspDate = (value: string): any => {
  const [year, month, day] = value.split('-')

  return new Date(`${year}-${month}-${day}`)
}

export const getValueEminEmax = (type: 'W_MIN' | 'W_MAX', inputValues: any, E_MAX_E_MIN: number) => {
  if (type === 'W_MIN') {
    const W_MAX: number | string =
      typeof inputValues?.W_MAX?.value === 'number' ? Number(inputValues?.W_MAX?.value) / 1000 : ''
    if (inputValues?.W_MIN?.calculate) {
      if (W_MAX === '') {
        return ''
      } else {
        const result = Number(W_MAX) - E_MAX_E_MIN
        const roundedResult = roundingTo3DigitsEminEmaxPgen(result)

        return String(result <= 0 ? 0 : roundedResult)
      }
    }

    return typeof inputValues?.W_MIN?.value === 'number' ? Number(inputValues?.W_MIN?.value / 1000) : ''
  }
  if (type === 'W_MAX') {
    return typeof inputValues?.W_MAX?.value === 'number'
      ? roundingTo3DigitsEminEmaxPgen(inputValues?.W_MAX?.value / 1000)
      : ''
  }
}

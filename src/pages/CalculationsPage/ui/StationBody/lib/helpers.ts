import { ICalculationRow, IRguCalculation } from 'entities/api/calculationsManager.entities.ts'
import { PlaningStageCategory, PlanningStage, RegistryType } from 'entities/shared/common.entities'
import { IAcceptErrorResponse } from 'entities/store/calculationPageStore.entities'
import { ITempGesCell } from 'entities/widgets/Vault.entities.ts'
import Handsontable from 'handsontable'
import { Plant } from 'pages/CalculationsPage/ui/StationBody/entities'
import { Dispatch, RefObject, SetStateAction } from 'react'
import { getSpreadsheetSelectedCells, SpreadsheetSelectedCells } from 'widgets/Spreadsheet/ui/lib'
import { IInputResultItemProp } from 'widgets/Spreadsheet/ui/Spreadsheet.tsx'

export const isRgu = (key: string) => {
  return key?.split('-')?.length > 1
}

interface objAllowedZones {
  topLine: number
  bottomLine: number
}

export const keysGES = [
  'P_MIN_RESULT',
  'P_GEN',
  'P_MAX_RESULT',
  'RESERVES_MAX',
  'AVRCHM_LOAD',
  'P_MIN',
  'P_MAX',
  'CM_P_MIN',
  'CM_P_MAX',
  'MODES_P_MIN',
  'MODES_P_MAX',
]

export enum typeKeysGes {
  P_MIN_RESULT = 'P_MIN_RESULT',
  P_GEN = 'P_GEN',
  P_MAX_RESULT = 'P_MAX_RESULT',
  RESERVES_MAX = 'RESERVES_MAX',
  AVRCHM_LOAD = 'AVRCHM_LOAD',
  P_MIN = 'P_MIN',
  P_MAX = 'P_MAX',
  CM_P_MIN = 'CM_P_MIN',
  CM_P_MAX = 'CM_P_MAX',
  MODES_P_MIN = 'MODES_P_MIN',
  MODES_P_MAX = 'MODES_P_MAX',
  CONSUMPT = 'CONSUMPT',
}

type valueValidType = string | number | null | undefined
type finalValueValidType = number | undefined
type typeObjectValidate = string

const checkValue = (value: valueValidType): finalValueValidType => {
  if (typeof value === 'string' && value.trim() === '') return undefined

  const numericValue = Number(value)

  return Number.isNaN(numericValue) ? undefined : +numericValue.toFixed(3)
}

interface validObjectType {
  is_Valid_P_GEN: boolean
  is_Valid_AVRCHM_LOAD: boolean
  is_Valid_P_MIN: boolean
  is_Valid_P_MAX: boolean
  is_Valid_CM_P_MIN: boolean
  is_Valid_CM_P_MAX: boolean
}

interface additionalValidationPlantProps {
  object: ITempGesCell
  value: number
  key: typeKeysGes
  regulatedUnit?: RegistryType
  validObject: validObjectType
}

const additionalValidationPlant_PLANT = () => {
  return undefined
}
const additionalValidationPlant_RGU = (props: additionalValidationPlantProps) => {
  const { key, validObject } = props
  let validateMessage = ``
  switch (key) {
    case typeKeysGes.P_GEN:
      if (validObject.is_Valid_P_GEN) {
        validateMessage = validateMessage + `Сумма Итог(план) РГЕ ≠ Итог (план) станции` + `\n`
      }
      break
    case typeKeysGes.AVRCHM_LOAD:
      if (validObject.is_Valid_AVRCHM_LOAD) {
        validateMessage = validateMessage + `Сумма АВРЧМ РГЕ ≠ АВРЧМ станции` + `\n`
      }
      break
    case typeKeysGes.P_MIN:
      if (validObject.is_Valid_P_MIN) {
        validateMessage = validateMessage + `Сумма Итог.огр(мин) РГЕ > Итог.огр(мин) станции` + `\n`
      }
      break
    case typeKeysGes.P_MAX:
      if (validObject.is_Valid_P_MAX) {
        validateMessage = validateMessage + `Сумма Итог.огр(макс) РГЕ < Итог.огр(макс) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MIN:
      if (validObject.is_Valid_CM_P_MIN) {
        validateMessage = validateMessage + `Сумма РМ(мин) РГЕ > РМ(мин) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MAX:
      if (validObject.is_Valid_CM_P_MAX) {
        validateMessage = validateMessage + `Сумма РМ(макс) РГЕ < РМ(макс) станции` + `\n`
      }
      break
    default:
      break
  }
  if (validateMessage.length > 0) {
    return validateMessage
  } else {
    return undefined
  }
}
export const additionalValidationPlant = ({ regulatedUnit, ...props }: additionalValidationPlantProps) => {
  switch (regulatedUnit) {
    case 'RGU':
      return additionalValidationPlant_RGU(props)
    case 'PLANT':
      return additionalValidationPlant_PLANT()
    default:
      return undefined
  }
}
export const additionalValidationRgu = (key: typeKeysGes, validObject: validObjectType) => {
  let validateMessage = ``
  switch (key) {
    case typeKeysGes.P_GEN:
      if (validObject.is_Valid_P_GEN) {
        validateMessage = validateMessage + `Сумма Итог(план) РГЕ ≠ Итог (план) станции` + `\n`
      }
      break
    case typeKeysGes.AVRCHM_LOAD:
      if (validObject.is_Valid_AVRCHM_LOAD) {
        validateMessage = validateMessage + `Сумма АВРЧМ РГЕ ≠ АВРЧМ станции` + `\n`
      }
      break
    case typeKeysGes.P_MIN:
      if (validObject.is_Valid_P_MIN) {
        validateMessage = validateMessage + `Сумма Итог.огр(мин) РГЕ > Итог.огр(мин) станции` + `\n`
      }
      break
    case typeKeysGes.P_MAX:
      if (validObject.is_Valid_P_MAX) {
        validateMessage = validateMessage + `Сумма Итог.огр(макс) РГЕ < Итог.огр(макс) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MIN:
      if (validObject.is_Valid_CM_P_MIN) {
        validateMessage = validateMessage + `Сумма РМ(мин) РГЕ > РМ(мин) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MAX:
      if (validObject.is_Valid_CM_P_MAX) {
        validateMessage = validateMessage + `Сумма РМ(макс) РГЕ < РМ(макс) станции` + `\n`
      }
      break
    default:
      break
  }
  if (validateMessage.length > 0) {
    return validateMessage
  } else {
    return undefined
  }
}
export const additionalValidation = (
  type: typeObjectValidate,
  object: ITempGesCell,
  value: valueValidType,
  key: typeKeysGes,
  regulatedUnit: RegistryType | undefined,
  validObject: validObjectType,
) => {
  const finValue = checkValue(value)
  if (finValue !== undefined) {
    switch (type) {
      case 'PLANT':
        return additionalValidationPlant({
          object,
          value: finValue,
          key,
          regulatedUnit,
          validObject,
        })
      case 'RGU':
        return additionalValidationRgu(key, validObject)
      default:
        return undefined
    }
  } else {
    return undefined
  }
}

export const getStatusCell = (
  isMaxConsumptionHour: boolean,
  isMinConsumptionHour: boolean,
  editor: Handsontable.GridSettings['editor'],
  keyStation: string,
  type: string,
  isValid: boolean,
  hasManualAdjustments?: boolean,
) => {
  const plantKeyData = keyStation.split('-')
  const plantKey = plantKeyData[1] ?? plantKeyData[0]
  const classes = [
    {
      // type содержит 'plantOptimized' или 'plantNotOptimized'
      className: type,
      enabled: plantKey === typeKeysGes.P_GEN && type.includes('plantNotOptimized'),
    },
    {
      // type содержит 'plantOptimized' или 'plantNotOptimized'
      className: type,
      enabled:
        (plantKey === typeKeysGes.P_MIN_RESULT || plantKey === typeKeysGes.P_MAX_RESULT) &&
        type.includes('plantOptimized'),
    },
    {
      className: 'borderLeft',
      enabled: plantKey === typeKeysGes.P_MIN_RESULT && type === 'plantOptimized',
    },
    {
      className: 'plantLimit',
      enabled: plantKey === typeKeysGes.P_MIN || plantKey === typeKeysGes.P_MAX,
    },
    {
      className: 'plantLimitRgu',
      enabled: (plantKey === typeKeysGes.P_MIN || plantKey === typeKeysGes.P_MAX) && plantKeyData.length > 1,
    },
    {
      className: 'plantModes',
      enabled: plantKey === typeKeysGes.MODES_P_MIN || plantKey === typeKeysGes.MODES_P_MAX,
    },
    {
      className: 'isNotValid',
      enabled: !isValid,
    },
    {
      className: 'minConsumptionHour',
      enabled: !(isMaxConsumptionHour && isMinConsumptionHour) && isMinConsumptionHour,
    },
    {
      className: 'maxConsumptionHour',
      enabled: (isMaxConsumptionHour && isMinConsumptionHour) || isMaxConsumptionHour,
    },
    {
      className: 'disabledCell',
      enabled: typeof editor !== 'string',
    },
    {
      className: 'manualAdjustments',
      enabled: hasManualAdjustments,
    },
  ]

  return classes
    .filter((classConfig) => classConfig.enabled)
    .map((classConfig) => classConfig.className)
    .join(' ')
}

export const updateManualAdjustmentStatusForCell = (className: string, hasManualAdjustments?: boolean) => {
  let manualAdjustments: string = ``
  if (hasManualAdjustments && !className.includes('manualAdjustments')) {
    manualAdjustments = 'manualAdjustments'
  } else if (!hasManualAdjustments) {
    return className.replace('manualAdjustments', '')
  }

  return `${className} ${manualAdjustments}`
}

const prepareToFixedValue = (value: string | number | null | undefined) => {
  return Number(Number(value).toFixed(3))
}

export const getValidGES = (
  _: number | null,
  key: string,
  object: ITempGesCell,
  value: valueValidType,
  inputResultProps?: IInputResultItemProp,
): string | undefined => {
  let validateMessage = ``
  const finValue = checkValue(value)
  if (finValue !== undefined) {
    if (key === 'P_MIN_RESULT') {
      const allowedZones = object?.allowedZones ?? []
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_MAX_RESULT`])) {
        validateMessage = validateMessage + `Итог(мин) > Итог(макс)` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_GEN`])) {
        validateMessage = validateMessage + `Итог(мин) > Итог(план)` + `\n`
      }
      if (allowedZones.length > 0) {
        const isAllowedZones =
          allowedZones.length > 0
            ? !!object?.allowedZones?.some((el: objAllowedZones) => finValue >= el.bottomLine && finValue <= el.topLine)
            : false
        if (!isAllowedZones) {
          validateMessage = validateMessage + `Итог(мин) вне доп. зон` + `\n`
        }
      }
      if (
        inputResultProps &&
        inputResultProps.wMax.value !== '' &&
        Number(inputResultProps.wMax.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эмакс = 0, то Эмин и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'P_GEN') {
      const allowedZones = object?.allowedZones ?? []
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог(план) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_MIN_RESULT`])) {
        validateMessage = validateMessage + `Итог(план) < Итог(мин)` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_MAX_RESULT`])) {
        validateMessage = validateMessage + `Итог(план) > Итог(макс)` + `\n`
      }
      if (allowedZones.length > 0) {
        const isAllowedZones =
          allowedZones.length > 0
            ? !!object?.allowedZones?.some((el: objAllowedZones) => finValue >= el.bottomLine && finValue <= el.topLine)
            : false
        if (!isAllowedZones) {
          validateMessage = validateMessage + `Итог(план) вне доп. зон` + `\n`
        }
      }
      if (
        inputResultProps &&
        inputResultProps.pGen.value !== '' &&
        Number(inputResultProps.pGen.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эплан = 0, то все значения Итог(план) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'P_MAX_RESULT') {
      const allowedZones = object?.allowedZones ?? []
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_MIN_RESULT`])) {
        validateMessage = validateMessage + `Итог(макс) < Итог(мин)` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_GEN`])) {
        validateMessage = validateMessage + `Итог(макс) < Итог(план)` + `\n`
      }
      if (allowedZones.length > 0) {
        const isAllowedZones =
          allowedZones.length > 0
            ? !!object?.allowedZones?.some((el: objAllowedZones) => finValue >= el.bottomLine && finValue <= el.topLine)
            : false
        if (!isAllowedZones) {
          validateMessage = validateMessage + `Итог(макс) вне доп. зон` + `\n`
        }
      }
      if (
        inputResultProps &&
        inputResultProps.wMax.value !== '' &&
        Number(inputResultProps.wMax.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эмакс = 0, то Эмин и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'RESERVES_MAX') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Rмакс < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`AVRCHM_LOAD`])) {
        validateMessage = validateMessage + `Rмакс < АВРЧМ` + `\n`
      }
    }
    if (key === 'AVRCHM_LOAD') {
      if (finValue < 0) {
        validateMessage = validateMessage + `АВРЧМ < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`RESERVES_MAX`])) {
        validateMessage = validateMessage + `АВРЧМ > Rмакс ` + `\n`
      }
      if (
        inputResultProps &&
        inputResultProps.pGen.value !== '' &&
        Number(inputResultProps.pGen.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эплан = 0, то все значения Итог(план) и АВРЧМ должны быть равны 0\n'
      }
      if (
        inputResultProps &&
        inputResultProps.wMax.value !== '' &&
        Number(inputResultProps.wMax.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эмакс = 0, то Эмин и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'P_MIN') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог.огр(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_MAX`])) {
        validateMessage = validateMessage + `Итог.огр(мин) > Итог.огр(макс)` + `\n`
      }
    }
    if (key === 'P_MAX') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог.огр(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_MIN`])) {
        validateMessage = validateMessage + `Итог.огр(макс) < Итог.огр(мин)` + `\n`
      }
    }
    if (key === 'CM_P_MIN' && prepareToFixedValue(object[`CM_P_MAX`]) !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `РМ(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`CM_P_MAX`])) {
        validateMessage = validateMessage + `РМ(мин) > РМ(макс)` + `\n`
      }
    }
    if (key === 'CM_P_MAX' && object[`CM_P_MIN`] !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `РМ(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`CM_P_MIN`])) {
        validateMessage = validateMessage + `РМ(макс) < РМ(мин)` + `\n`
      }
    }
    if (key === 'MODES_P_MIN' && object[`MODES_P_MAX`] !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `Модес(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`MODES_P_MAX`])) {
        validateMessage = validateMessage + `Модес(мин) > Модес(макс)` + `\n`
      }
    }
    if (key === 'MODES_P_MAX' && object[`MODES_P_MIN`] !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `Модес(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`MODES_P_MIN`])) {
        validateMessage = validateMessage + `Модес(макс) < Модес(мин)` + `\n`
      }
    }
    if (key === 'CONSUMPT') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Потр < 0` + `\n`
      }
    }
  }
  if (validateMessage.length > 0) {
    return validateMessage
  } else {
    return undefined
  }
}

export const ACTUAL_ITEM = {
  value: 'ACTUAL',
  label: 'Актуальный',
  color: '#000000',
}

export const hours = new Array(24).fill(null).map((_, index) => String(index + 1))

export const getPrepareDate = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
  const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`

  return `${year}-${month}-${day}`
}

export const downloadTheSourceData = (
  type: string,
  selectedStage: string,
  actualStage: { code: number | string },
  date: Date,
  selectLeftMenu: number,
  onDownloadTheSourceData: (
    selectLeftMenu: number,
    date: string,
    type: string,
    planingStage: string | number,
    dateISP: string,
  ) => void,
  dateISP: string,
) => {
  const planingStage = selectedStage === ACTUAL_ITEM.value ? actualStage?.code : selectedStage
  if (type === 'LOAD_PLANT_DATA') {
    const prepareDate = getPrepareDate(date)
    onDownloadTheSourceData(selectLeftMenu, prepareDate, 'LOAD_PLANT_DATA', planingStage, dateISP)
    onDownloadTheSourceData(selectLeftMenu, prepareDate, 'LOAD_GENERATOR_ALLOWED_ZONES', planingStage, dateISP)
  } else {
    const prepareDate = getPrepareDate(date)
    onDownloadTheSourceData(selectLeftMenu, prepareDate, type, planingStage, dateISP)
  }
}

export const handleAcceptObject = async (
  date: Date,
  selectedStage: string,
  setAcceptObject: (selectLeftMenu: number, date: string, selectedStage: string, forced: boolean) => Promise<boolean>,
  selectLeftMenu: number,
  setIsAccepted: Dispatch<SetStateAction<boolean | null>>,
  initLoadData: VoidFunction,
  forced: boolean,
  setData: (data: Plant[]) => void,
  data: Plant[],
) => {
  const prepareDate = getPrepareDate(date)

  try {
    await setAcceptObject(selectLeftMenu, prepareDate, selectedStage, forced)

    initLoadData()
    setData(data.map((el) => (el.plantId === selectLeftMenu ? { ...el, accepted: true } : el)))

    setIsAccepted(null)
  } catch (e) {
    const error = e as IAcceptErrorResponse

    if (error.status === 400) {
      setIsAccepted(null) // Скрывает AcceptModal только в случае 400 ошибки
    } else {
      console.error('Ошибка в handleAcceptObject:', e)
    }

    throw e
  }
}

export const handleDisacceptObject = async (
  date: Date,
  selectedStage: string,
  selectLeftMenu: number,
  setDisacceptObject: (select: number, date: string, stage: string) => Promise<void>,
  setIsAccepted: Dispatch<SetStateAction<boolean | null>>,
  initLoadData: VoidFunction,
  setData: (data: Plant[]) => void,
  data: Plant[],
) => {
  const prepareDate = getPrepareDate(date)

  try {
    await setDisacceptObject(selectLeftMenu, prepareDate, selectedStage)
    initLoadData()
    setData(data.map((el) => (el.plantId === selectLeftMenu ? { ...el, accepted: false } : el)))
    setIsAccepted(null)
  } catch (error) {
    console.error('Ошибка в handleDisacceptObject:', error)
    throw error
  }
}

export const resizeWebUp = (
  refUp: RefObject<HTMLDivElement>,
  setHeightUp: Dispatch<SetStateAction<number | undefined>>,
  setWidthUp: Dispatch<SetStateAction<number | undefined>>,
) => {
  if (refUp) {
    const curHeight = refUp?.current?.getBoundingClientRect()?.height
    const curWidth = refUp?.current?.getBoundingClientRect()?.width
    setHeightUp(curHeight)
    setWidthUp(curWidth)
  }
}

export const resize = (
  refUp: RefObject<HTMLDivElement>,
  setHeightUp: Dispatch<SetStateAction<number | undefined>>,
  setWidthUp: Dispatch<SetStateAction<number | undefined>>,
) => {
  resizeWebUp(refUp, setHeightUp, setWidthUp)
}

export const getColorStage = (key: PlanningStage, finished: boolean) => {
  if (finished) {
    return 'planningStagesDoNotMatchHeaderLabel'
  }
  if (key === 'PER' || key === 'PDG') {
    return 'RSVHeaderLabel'
  }
  if (key === 'VSVGO1' || key === 'VSVGO2' || key === 'VSVGO3') {
    return 'VSVGOHeaderLabel'
  }

  return ''
}

export const getColorByCategory = (category?: PlaningStageCategory, corresponds?: boolean) => {
  if (!corresponds) {
    return 'planningStagesDoNotMatchHeaderLabel'
  }
  if (category === PlaningStageCategory.RSV) {
    return 'RSVHeaderLabel'
  }
  if (category === PlaningStageCategory.VSVGO) {
    return 'VSVGOHeaderLabel'
  }

  return ''
}

const gesStationBlockByLevels = [
  ['P_MIN_RESULT', 'P_GEN', 'P_MAX_RESULT'],
  ['RESERVES_MAX', 'AVRCHM_LOAD'],
  ['LIMIT_MIN', 'LIMIT_MAX'],
  ['CM_P_MIN', 'CM_P_MAX'],
  ['MODES_P_MIN', 'MODES_P_MAX'],
]
export const getGesCellPropByTableCoords = (
  rows: ICalculationRow[],
  rgus: IRguCalculation[],
  rowId: number,
  colId: number,
) => {
  const stationDataForHour = rows[rowId]
  const rguLength = rgus.length
  const resulColumnLength = 3 * (rguLength + 1)
  const baseColumnLength = 2 * (rguLength + 1)
  let changedColNumInFirstLevel = 0
  let changedColNumInSecondLevel = colId
  if (colId >= resulColumnLength + baseColumnLength * 4) return

  while (
    (changedColNumInSecondLevel >= resulColumnLength && changedColNumInFirstLevel === 0) ||
    (changedColNumInSecondLevel >= baseColumnLength && changedColNumInFirstLevel > 0)
  ) {
    if (changedColNumInFirstLevel === 0) {
      changedColNumInSecondLevel -= resulColumnLength
    } else {
      changedColNumInSecondLevel -= baseColumnLength
    }
    changedColNumInFirstLevel++
  }
  // Индекс указывающий на то, где произошло изменения: в станции или по одной из РГЕ
  let changedBlock =
    Math.ceil(changedColNumInSecondLevel / gesStationBlockByLevels[changedColNumInFirstLevel].length) - 1
  // Индекс указывающий на то, какая именно колонка изменилась
  const colNumInsideChangedBlock =
    changedColNumInSecondLevel % gesStationBlockByLevels[changedColNumInFirstLevel].length
  if (
    colNumInsideChangedBlock === 0 &&
    ((changedColNumInFirstLevel === 0 && changedColNumInSecondLevel < resulColumnLength) ||
      (changedColNumInFirstLevel > 0 && changedColNumInSecondLevel < baseColumnLength))
  ) {
    changedBlock++
  }

  if (changedBlock <= 0) {
    return stationDataForHour.cells.find(
      (cell) => cell.column === gesStationBlockByLevels[changedColNumInFirstLevel][colNumInsideChangedBlock],
    )
  } else {
    return rgus[changedBlock - 1].rows[rowId].cells.find(
      (cell) => cell.column === gesStationBlockByLevels[changedColNumInFirstLevel][colNumInsideChangedBlock],
    )
  }
}

export const handleSelectedCoords = (
  hot: Handsontable | null,
  setSelectedCells: (cells: SpreadsheetSelectedCells[]) => void,
  setOutsideTableClickCounter: (outsideTableClickCounter: number) => void,
) => {
  const transformedSelectedRange = getSpreadsheetSelectedCells(hot)
  setSelectedCells(transformedSelectedRange)
  setOutsideTableClickCounter(0)
}

export const getStyledComment = (value: string | undefined, readOnly = true) => {
  if (!value) return { value, readOnly }

  // Константы для расчёта ширины и высоты комментария
  const CHAR_WIDTH = 6.6
  const CHAR_W_PADDING = 16
  const CHAR_HEIGHT = 15
  const CHAR_H_PADDING = 10

  const trimmedValue = value.trim()
  const lines = trimmedValue.split('\n')
  const maxLineLength = Math.max(...lines.map((line) => line.length))

  // Динамически вычисляем ширину и высоту комментария по содержимому
  const style = {
    width: CHAR_WIDTH * maxLineLength + CHAR_W_PADDING,
    height: CHAR_HEIGHT * lines.length + CHAR_H_PADDING,
  }

  return { value: trimmedValue, readOnly, style }
}

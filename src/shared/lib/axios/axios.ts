import { Client, IStompSocket } from '@stomp/stompjs'
import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import { addDays, format } from 'date-fns'
import { getPrepareDate, IStage } from 'entities/pages/calcPage.entities.tsx'
import { IAsyncTask, PlanningStage, PlanningStageRussia, TaskStatus } from 'entities/shared/common.entities.ts'
import { ACTUAL_ITEM } from 'pages/CalculationsPage/ui/StationBody/lib'
import { formatDateTime } from 'shared/lib/dateFormates'
import { jwtDecode } from 'shared/lib/jwtDecode'
import { getRefreshToken, getToken, setTokens } from 'shared/lib/localStorage/localStorage'
import { locationParse } from 'shared/lib/locationParse'
import SockJS from 'sockjs-client'
import { stores } from 'stores/RootStore'

import { ForbiddenError, ServerError } from './Error'

interface IMessageOutput {
  body: string
}

export enum WebSocketStatus {
  CONNECTED = 'CONNECTED',
  CONNECTING = 'CONNECTING',
  DISCONNECTED = 'DISCONNECTED',
  STOMP_ERROR = 'STOMP_ERROR',
  WS_CLOSED = 'WS_CLOSED',
  WS_ERROR = 'WS_ERROR',
}

interface StompClientType extends Omit<Client, 'webSocketFactory'> {
  webSocketFactory: (() => IStompSocket) | (() => WebSocket)
}

let stompClient: StompClientType | null = null

const retryConnectionWebSocket = () => {
  if (stompClient?.active) {
    stompClient?.deactivate().then(() => {
      isViewWSLog && console.log('>>> Соединение прервано на клиенте')
      const token = getToken()
      const isValidToken = checkTokenTime(getToken())

      if (token && !isValidToken) {
        updateToken().then(async () => {
          isViewWSLog && console.log('>>> Обновили токен и инициируем повторное соединение')
          await connect()
        })
      } else {
        isViewWSLog && console.log('>>> Обновили токен и инициируем повторное соединение')
        connect()
      }
    })
  }
}

const getTypeNotification = (status: 'DONE' | 'FAILED') => {
  switch (status) {
    case 'DONE':
      return 'success'
    case 'FAILED':
    default:
      return 'error'
  }
}

export async function connect() {
  isViewWSLog && console.log('START CONNECTION')
  const token = getToken()
  const isValidToken = checkTokenTime(getToken())
  isViewWSLog && console.log('TOKEN', { isValidToken })
  if (token && !isValidToken) {
    await updateToken().then(() => {
      connect()
    })

    return
  } else if (!token) {
    isViewWSLog && console.log('ZERO TOKEN', { token })
    await disconnectWebSocket()
    window.location.replace('/login')

    return
  }
  const stompClientOptions = {
    connectHeaders: {
      Authorization: token,
    },
  }
  isViewWSLog && console.log('ACTIVITY', { stompClient, active: stompClient?.active })
  if (!stompClient?.active) {
    stompClient = new Client(stompClientOptions) as StompClientType
    stompClient.connectionTimeout = 5000
  }
  const url = '/api/ws'
  if (stompClient !== null) {
    stompClient.webSocketFactory = function () {
      const parsedUrl = new URL(url, window.location.href)
      if (parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:') {
        return new SockJS(url)
      }
      if (parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:') {
        return new WebSocket(url)
      }
      throw new Error('Protocol not supported')
    }
    if (isViewWSLog) {
      stompClient.debug = (msg: string) => {
        console.group('>> MESSAGE FROM SOCKET')
        console.log(formatDateTime(new Date()))
        console.log(msg)
        console.groupEnd()
      }
      stompClient.onWebSocketError = function () {
        console.log(WebSocketStatus.WS_ERROR, 'Повторное подключение инициирует библиотека STOMP')
      }
    }
    stompClient.onWebSocketClose = function () {
      isViewWSLog && console.log(WebSocketStatus.WS_CLOSED, 'Повторное подключение инициирует КЛИЕНТ')
      retryConnectionWebSocket()
    }
    stompClient.onDisconnect = function () {
      isViewWSLog && console.log(WebSocketStatus.DISCONNECTED, 'Повторное подключение инициирует КЛИЕНТ')
      retryConnectionWebSocket()
    }
    stompClient.onStompError = function () {
      isViewWSLog && console.log(WebSocketStatus.STOMP_ERROR, 'Повторное подключение инициирует КЛИЕНТ')
      retryConnectionWebSocket()
    }
    if (!stompClient.active) {
      isViewWSLog && console.log('>>> Соединение неактивно, активируем повторно')
      stompClient.activate()
    }

    stompClient.onConnect = function () {
      if (stompClient && stompClient.connected) {
        stompClient?.subscribe('/neptune/common/async-task', function (messageOutput: IMessageOutput) {
          const data = JSON.parse(messageOutput.body as string) ?? {}
          const initials: string = stores?.authStore?.userDetail?.initials ?? ''
          const userFio: string = data.userFio ?? ''

          if (location.pathname === '/nsi') {
            if (location.search.includes('referencedata') && data?.type?.code === 'SYNC_REFERENCE_DATA') {
              if (data.status === 'IN_PROCESS') {
                stores.referenceDataStore.setLoadingStatusRefDataSk11(data.status)
              }
              if (data.status === 'DONE') {
                stores.referenceDataStore.getLastTaskInfoReferenceData()
                if (initials === userFio) {
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.resultMessage,
                    type: 'success',
                  })
                }
              }
              if (data.status === 'FAILED') {
                stores.referenceDataStore.getLastTaskInfoReferenceData()
                stores.notificationStore.addNotification({
                  title: data.type.title,
                  description: data?.warnings?.length > 0 ? data.warnings.join(' ;') : data.resultMessage,
                  type: 'error',
                  multiError: true,
                })
              }
            } else if (!location.search.includes('referencedata') && data?.type?.code === 'SYNC_REGISTRY') {
              if (data.status === 'IN_PROCESS') {
                stores.nsiStore.startStatusNsi(data.status)
              }
              if (data.status === 'DONE') {
                stores.nsiStore.resetStatusNsi()
                stores.nsiStore.getLastTaskInfoListOfObjects('SYNC_REGISTRY')
                const type = stores?.nsiStore?.hierarchyType ?? 'BY_RGU'
                stores.nsiStore.getRegistry(type)
              }
              if (data.status === 'FAILED') {
                stores.nsiStore.resetStatusNsi()
                stores.nsiStore.getLastTaskInfoListOfObjects('SYNC_REGISTRY')
              }
              if (data.status === 'DONE' && initials === userFio) {
                stores.notificationStore.addNotification({
                  title: data.type.title,
                  description: data.resultMessage,
                  type: 'success',
                })
              }
            }
          } else if (location.pathname === '/calculations') {
            const { params } = data
            const [yearApi, monthApi, dayApi] = params.date.split('-')
            const date = stores.calculationsPageStore.date
            const depId = stores.calculationsPageStore.selectLeftMenu
            const stage = stores.calculationsPageStore.getStage(stores.calculationsPageStore.selectedStage)
            const year = new Date(date).getFullYear()
            const month = new Date(date).getMonth() + 1
            const day = new Date(date).getDate()
            const isView =
              year === Number(yearApi) &&
              month === Number(monthApi) &&
              day === Number(dayApi) &&
              Number(depId) === Number(params?.plantId) &&
              stage === params.stage
            if (
              year === Number(yearApi) &&
              month === Number(monthApi) &&
              day === Number(dayApi) &&
              depId === 0 &&
              stage === params.stage
            ) {
              if (stores.calculationsPageStore.vaultStore.isModalUpdateVault) {
                const { type, userFio, updatedDate, status, warnings = [] } = data
                stores.calculationsPageStore.vaultStore.updateModalUpdateVaultByIdAndKey(
                  params?.plantId,
                  type.code,
                  status,
                  userFio,
                  warnings,
                  updatedDate,
                )
              }
            }

            if (isView) {
              if (!data?.warnings?.length && data.resultMessage) {
                stores.notificationStore.addNotification({
                  title: data.type.title,
                  description: data.resultMessage,
                  type: getTypeNotification(data.status),
                })
              }
              stores.calculationsPageStore.updateCalcPossibilityTaskStatus(data as IAsyncTask)
              if (data.status === 'IN_PROCESS') {
                stores.calculationsPageStore.startLoadTheSourceData()
              }
              if (data.status === 'DONE') {
                const { date, stage } = data.params
                stores.calculationsPageStore.stopLoadTheSourceData(data?.params?.plantId, date, stage, true)
                if (data?.warnings?.length > 0) {
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.warnings.join(' ;'),
                    type: 'error',
                    multiError: true,
                  })
                }
              }
              if (data.status === 'FAILED') {
                const { date, stage } = data.params
                stores.calculationsPageStore.startLoadTheSourceData()
                stores.calculationsPageStore.stopLoadTheSourceData(data?.params?.plantId, date, stage, false)
                if (data?.warnings?.length > 0) {
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.warnings.join(' ;'),
                    type: 'error',
                    multiError: true,
                  })
                }
              }
            }

            //
            // Загрузка АВРЧМ ТЭС из Модеса
            if (data.type.code === 'LOAD_AVRCHM') {
              let { year = null, month = null, day = null } = locationParse(location.search)

              if (!(year && month && day)) {
                ;[year, month, day] = format(addDays(new Date(), 1), 'yyyy-MM-dd').split('-')
              }

              const currentStage =
                stores.calculationsPageStore.selectedStage === 'ACTUAL'
                  ? (stores?.calculationsPageStore?.actualStage?.code as string)
                  : stores.calculationsPageStore.stages?.find((el: { value: string }) => {
                      return el.value === stores.calculationsPageStore.selectedStage
                    })?.value

              if (['DONE', 'FAILED'].includes(data.status))
                stores.calculationsPageStore.vaultStore.isLoadingAvrchmTes = false

              if (
                +year === +yearApi &&
                +month === +monthApi &&
                +day === +dayApi &&
                (stores.calculationsPageStore.currentPlantId === null ||
                  +stores.calculationsPageStore.currentPlantId === 0) &&
                currentStage === params.stage
              ) {
                if (data.status === 'IN_PROCESS') {
                  stores.calculationsPageStore.vaultStore.isLoadingAvrchmTes = true
                }

                if (data.status === 'DONE') {
                  stores.calculationsPageStore.stopLoadTheSourceData(
                    0,
                    `${year}-${month}-${day}`,
                    currentStage as unknown as IStage,
                    true,
                  )
                  if (data?.warnings?.length > 0) {
                    stores.notificationStore.addNotification({
                      title: data.type.title,
                      description: data.warnings.join(' ;'),
                      type: 'error',
                      multiError: true,
                    })
                  } else {
                    stores.notificationStore.addNotification({
                      title: data.type.title,
                      description: data.resultMessage,
                      type: 'success',
                    })
                  }
                  if (stores.calculationsPageStore.selectedStage !== null) {
                    stores.calculationsPageStore.avrchmStore.initAvrchm(true)
                  }
                }
                if (data.status === 'FAILED') {
                  stores.calculationsPageStore.stopLoadTheSourceData(
                    0,
                    `${year}-${month}-${day}`,
                    currentStage as unknown as IStage,
                    true,
                  )
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.warnings?.length > 0 ? data.warnings.join(' ;') : data.resultMessage,
                    type: 'error',
                    ...(data.warnings?.length > 0 && { multiError: true }),
                  })
                }
              }
            }

            //
            // Загрузка фактических данных за сутки
            if (data.type.code === 'LOAD_PLANT_TELEMETRY') {
              if (
                stores.calculationsPageStore.telemetryTableDate === params.date &&
                stores.calculationsPageStore.currentPlantId === data.params.plantId
              ) {
                if (data.status === 'IN_PROCESS') {
                  stores.calculationsPageStore.setLoadingTelemetryByDateStatus(data.status)
                }
                if (data.status === 'DONE') {
                  stores.calculationsPageStore.getTelemetry(data.params.plantId, data.params.date, data.status)
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.resultMessage,
                    type: 'success',
                  })
                }
                if (data.status === 'FAILED') {
                  stores.calculationsPageStore.setLoadingTelemetryByDateStatus(data.status)
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.warnings?.length > 0 ? data.warnings.join(' ;') : data.resultMessage,
                    type: 'error',
                    ...(data.warnings?.length > 0 && { multiError: true }),
                  })
                }
              }
            }
          } else if (location.pathname === '/gaes-calculations') {
            const {
              params: { plantId = null, date = '' } = {},
              type: { code = '', title = '' } = {},
              status = '',
              resultMessage = '',
              warnings = [],
            } = data
            const selectedPlant = stores.gaesCalculationsStore.selectedPlant

            if (selectedPlant && plantId !== selectedPlant.plantId) return

            //
            // Загрузка фактических данных за сутки
            if (data.type.code === 'LOAD_PLANT_TELEMETRY') {
              if (
                stores.gaesCalculationsStore.gaesCalculationTabStore.telemetryDate === date &&
                stores.gaesCalculationsStore.selectedPlant?.plantId === data.params.plantId
              ) {
                if (data.status === 'IN_PROCESS') {
                  stores.gaesCalculationsStore.gaesCalculationTabStore.setLoadingTelemetryByDateStatus(data.status)
                }
                if (data.status === 'DONE') {
                  stores.gaesCalculationsStore.gaesCalculationTabStore.getTelemetry(
                    data.params.plantId,
                    data.params.date,
                    data?.status,
                  )
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.resultMessage,
                    type: 'success',
                  })
                }
                if (data.status === 'FAILED') {
                  stores.gaesCalculationsStore.gaesCalculationTabStore.setLoadingTelemetryByDateStatus(data.status)
                  stores.notificationStore.addNotification({
                    title: data.type.title,
                    description: data.warnings?.length > 0 ? data.warnings.join(' ;') : data.resultMessage,
                    type: 'error',
                    ...(data.warnings?.length > 0 && { multiError: true }),
                  })
                }
              }
            }

            if (code === 'LOAD_CONSUMPTION' || code === 'LOAD_PLANT_DATA') {
              if (status === 'IN_PROCESS') {
                stores.gaesCalculationsStore.updateGAESCalcPossibilityAsyncTaskStatus(code, TaskStatus.IN_PROCESS)
              }
              if (status === 'DONE') {
                stores.gaesCalculationsStore.updateGAESCalcPossibilityAsyncTaskStatus(code, TaskStatus.DONE)
                if (warnings.length > 0) {
                  stores.notificationStore.addNotification({
                    title: 'Обновление данных',
                    description: warnings.join(' ;'),
                    type: 'error',
                    multiError: true,
                  })
                }
                stores.notificationStore.addNotification({
                  title: 'Обновление данных',
                  description: 'Данные на форме обновлены',
                  type: 'actions',
                  action: () => {
                    stores.gaesCalculationsStore.gaesCalculationTabStore.getGAESCalculationSpreadsheetData(false)
                  },
                })
                stores.gaesCalculationsStore.debouncedCheckGAESCalcPossibility()
              }
            }

            if (status === 'FAILED') {
              stores.notificationStore.addNotification({
                title: title,
                description: warnings?.length > 0 ? warnings.join(' ;') : resultMessage,
                type: 'error',
                ...(warnings?.length > 0 && { multiError: true }),
              })
            }
          }
        })

        stompClient?.subscribe('/neptune/common/calculation-task', function (messageOutput: IMessageOutput) {
          if (location.pathname === '/calculations') {
            const data = JSON.parse(messageOutput.body as string) ?? {}
            const { status, date, planingStage } = data
            const currentSelectedDate = format(stores.calculationsPageStore.date, 'yyyy-MM-dd')
            const storedPlaningStage =
              stores?.calculationsPageStore?.selectedStage === ACTUAL_ITEM.value
                ? stores?.calculationsPageStore?.actualStage?.code
                : stores?.calculationsPageStore?.selectedStage

            if (
              currentSelectedDate === date &&
              stores.calculationsPageStore.selectLeftMenu === 0 &&
              planingStage === storedPlaningStage
            ) {
              stores.calculationsPageStore.vaultStore.changeStatusDataVault(status)
            }
            if (
              currentSelectedDate === date &&
              stores.calculationsPageStore.selectLeftMenu === 0 &&
              planingStage === storedPlaningStage &&
              status !== 'IN_PROCESS'
            ) {
              stores.calculationsPageStore.vaultStore.setShouldUpdateVault(true)
              stores.notificationStore.addNotification({
                title: 'Обновление данных',
                description: 'Данные на своде обновлены',
                type: 'actions',
                action: () => {
                  stores.calculationsPageStore.vaultStore.initLoadDataVault().then(() => {
                    stores.calculationsPageStore.avrchmStore.initAvrchm(true)
                    stores.calculationsPageStore.vaultStore.setShouldUpdateVault(false)
                  })
                },
              })
            }
          }
        })
        stompClient?.subscribe('/neptune/common/actual-planing-stage', function (messageOutput: IMessageOutput) {
          let objectDate = new Date()
          if (location.pathname === 'calcModel') {
            objectDate = new Date(stores.calcModelStore.date)
          }
          if (location.pathname === 'calculations') {
            objectDate = new Date(stores.calculationsPageStore.date)
          }
          const data = JSON.parse(messageOutput.body as string) ?? {}
          const { actualStage }: { actualStage: PlanningStage } = data
          const [yearApi, monthApi, dayApi] = data.date.split('-')
          const year = objectDate.getFullYear()
          const month = objectDate.getMonth() + 1
          const day = objectDate.getDate() + 1
          const actualTitle = PlanningStage[actualStage] ?? ''
          const isView =
            Number(year) === Number(yearApi) && Number(month) === Number(monthApi) && Number(day) === Number(dayApi)
          if (isView) {
            if (location.pathname === 'calcModel') {
              stores.calcModelStore.changeActualStage(actualStage, actualTitle)
              const planingStage =
                stores?.calculationsPageStore?.selectedStage === ACTUAL_ITEM.value
                  ? stores?.calcModelStore?.actualStage?.code
                  : stores?.calculationsPageStore?.selectedStage
              if (stores.calcModelStore.typeSort === 'alphabeat') {
                return stores.calcModelStore.changeSortType(
                  false,
                  getPrepareDate(stores.calculationsPageStore.date),
                  planingStage,
                )
              } else {
                return stores.calcModelStore.changeSortType(
                  true,
                  getPrepareDate(stores.calculationsPageStore.date),
                  planingStage,
                )
              }
            }
            if (location.pathname === 'calculations') {
              stores.calculationsPageStore.changeActualStage(actualStage, actualTitle)
              const stage =
                stores.calculationsPageStore.selectedStage === 'ACTUAL'
                  ? actualStage
                  : stores.calculationsPageStore.selectedStage
              const date = getPrepareDate(stores.calculationsPageStore.date)
              stores.notificationStore.addNotification({
                title: `Изменение планирования`,
                description: `Актуальный этап : ${PlanningStageRussia[actualTitle]}`,
                type: 'success',
              })
              if (stores.calculationsPageStore.selectLeftMenu === 0) {
                stores.calculationsPageStore.vaultStore.checkCalcPossibilityVault()
              } else {
                stores.calculationsPageStore.checkCalcPossibility(
                  date,
                  stage as PlanningStage,
                  stores.calculationsPageStore.selectLeftMenu,
                )
              }
            }
          }
        })
      }
    }
  }
}

export async function disconnectWebSocket() {
  if (stompClient !== null) {
    await stompClient.deactivate()
    isViewWSLog && console.log('>>> Соединение прервано на клиенте, stompClient = null')
    stompClient = null
  }
}
//

let isAuthentication: Promise<void> | null = null

const createMainAxiosInstance = () => {
  return axios.create({
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 900000,
  })
}

export const checkTokenTime = (token: string | null | undefined) => {
  if (token) {
    const tokenExpiretionTime = jwtDecode(token).exp as number
    const timeNow = Math.floor(Date.now() / 1000)

    return tokenExpiretionTime - timeNow > 1
  }

  return false // если не валидный
}

export const updateToken = async () => {
  const update = async () => {
    const refreshToken = getRefreshToken()
    try {
      const data = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },

        body: JSON.stringify({ refreshToken }),
      })
      if (data.status === 500) {
        const refreshToken = JSON.parse(getRefreshToken())
        const token = getToken()
        if (token && refreshToken) {
          return
        } else {
          window.location.replace('/login')
          await disconnectWebSocket()
        }
      }
      if (data.status === 401 || data.status === 400 || data.status === 403) {
        await disconnectWebSocket()
        if (!localStorage.getItem('wasTokenForciblyUpdated')) {
          isViewWSLog && console.log('SET wasTokenForciblyUpdated')
          localStorage.setItem('wasTokenForciblyUpdated', 'true')
          // контрольный вызов обновления токена
          await new Promise(function (resolve) {
            setTimeout(async function () {
              await update()
              resolve('updateToken')
            }, 5000)
          })
        } else {
          localStorage.removeItem('token')
          localStorage.removeItem('refreshToken')
          window.location.replace('/login')
        }
      } else {
        const response = await data.json()
        setTokens({
          token: response.access_token,
          refreshToken: response.refresh_token,
        })
      }
      isViewWSLog && console.log('REMOVE wasTokenForciblyUpdated')
      localStorage.removeItem('wasTokenForciblyUpdated')
      if (!stompClient?.active && !!refreshToken) {
        isViewWSLog && console.log('>>> Соединение неактивно, активируем повторно после обновления RefreshToken')
        await connect()
      }
    } catch (e) {
      isViewWSLog && console.log('error refresh', e)
      localStorage.removeItem('wasTokenForciblyUpdated')
    }
  }
  if (!isAuthentication) isAuthentication = update()
  await isAuthentication
  isAuthentication = null
}

const createRequestInterceptorAuth = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    async (config) => {
      let token = ''
      if (isAuthentication) {
        await isAuthentication
      }
      token = getToken()
      const isValidToken = checkTokenTime(token)
      if (token && !isValidToken) {
        await updateToken()
      }
      token = getToken()
      if (token) {
        config.headers.Authorization = token
      }

      return config
    },
    (err: string) => Promise.reject(err),
  )
}
type IDataResponse = string | { message: string; error: string }

export interface IResponse {
  status?: number
  data: IDataResponse | string
  headers: { 'retry-after': number }
  config: { url: string }
}

export interface IErrProps {
  message?: string
  response: IResponse
}

const createResponseInterceptorAuth = (instance: AxiosInstance) => {
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      /*
       * Проверяем, является ли тип ответа 'blob' (обычно используется для файлов).
       * Если да, то возвращаем объект response. Это нужно, чтобы иметь доступ к метаинформации,
       * связанной с файлом (например, имени файла из поля 'content-disposition' из response.headers).
       * Иначе возвращаем объект с данными ответа.
       */
      if (response.config.responseType === 'blob') {
        return response
      }

      return response.data
    },
    async (err: IErrProps) => {
      if (err?.response?.status === undefined) {
        // Пробрасываем через reject ошибку, что бы можно было ее обработать
        return Promise.reject(err)
      }
      if (err.message === 'request_canceled') {
        throw err.message
      }
      const isLoginPage = window.location.pathname === '/login'
      if (!isLoginPage) {
        if (err?.response?.status === 401 || err?.response?.status === 403) {
          // // рефреш токен устарел, сбрасываем авторизацию.
          await updateToken().then().catch()
        }
      }

      const errorDescription =
        typeof err?.response?.data === 'string'
          ? err?.response?.data
          : err?.response?.data?.error
            ? err?.response?.data?.error
            : err?.response?.data?.message

      if (err?.response?.status === 504) {
        stores.notificationStore.addNotification({
          title: '504',
          description: 'Время ожидания шлюза истекло',
          type: 'error',
        })

        return Promise.reject()
      } else if (err?.response?.status === 404) {
        stores.notificationStore.addNotification({
          title: 'Предупреждение',
          description: errorDescription,
          type: 'warning',
        })

        return Promise.reject()
      } else if (err?.response?.status === 423) {
        stores.notificationStore.addNotification({
          title: 'Предупреждение',
          description: errorDescription,
          type: 'warning',
        })

        return Promise.reject()
      } else if (err?.response?.status === 502) {
        stores.notificationStore.addNotification({
          title: 'Ошибка!',
          description: 'Сервис не доступен',
          type: 'error',
        })

        return
      } else if (err?.response?.status === 400) {
        stores.notificationStore.addNotification({
          title: 'Ошибка',
          description: errorDescription,
          type: 'error',
        })
        throw err?.response?.data
      } else if (err?.response?.status === 401) {
        if (err.response.config.url === '/api/v1/auth/login') {
          throw new Error(errorDescription)
        }

        return
      } else if (err?.response?.status === 429) {
        const RetryAfter = err.response.headers['retry-after']
        const time = RetryAfter ? Number(RetryAfter) * 1000 : 10000
        throw { code: 429, time }
      } else if (err?.response?.status === 500) {
        throw new ServerError(errorDescription ? errorDescription : '')
      } else if (err?.response?.status === 403) {
        throw new ForbiddenError(errorDescription ? errorDescription : '')
      } else if (err?.response?.status === 409) {
        throw err?.response?.data
      } else {
        stores.notificationStore.addNotification({
          title: 'Ошибка',
          description: errorDescription,
          type: 'error',
        })
        throw new Error(errorDescription)
      }
    },
  )
}

const axiosInstance = createMainAxiosInstance()

createRequestInterceptorAuth(axiosInstance)
createResponseInterceptorAuth(axiosInstance)

export { axiosInstance }

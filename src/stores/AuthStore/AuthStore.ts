import { type IAuthFormData, type IUserDetails, type IUserRole } from 'entities/store/auth.entities.ts'
import { makeAutoObservable, runInAction } from 'mobx'
import api from 'shared/api/index'
import { checkTokenTime, connect, updateToken } from 'shared/lib/axios/axios'
import { clearTokensAndInformation, getTokensAndInformation, setTokensAndInformation } from 'shared/lib/localStorage'
import type { RootStore } from 'stores/RootStore.ts'

export class AuthStore {
  rootStore: RootStore
  domainList: Array<{ value: string; label: string; disabled?: boolean }> = []
  userDetail: IUserDetails = { roles: [], initials: '' }
  loginError = ''
  isAuthProgress = false
  isAuth = false
  count = 0
  notifications = { nsi: {} }
  appVersion: string | null = null

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  getAppVersion = async () => {
    try {
      const { version } = await api.userManager.getAppVersion()
      if (version) {
        runInAction(() => {
          this.appVersion = version
        })
      }
    } catch (e) {
      console.log('e', e)
    }
  }

  getDomains = async () => {
    try {
      const { domains } = await api.userManager.getDomains()
      if (domains) {
        runInAction(() => {
          this.domainList = domains.map((item: { value: string; label: string }) => ({
            value: item,
            label: item,
            icon: 'domain',
          }))
        })
      }
    } catch (e) {
      console.log('e', e)
    }
  }

  login = async (params: IAuthFormData) => {
    try {
      runInAction(() => {
        this.isAuthProgress = true
        this.loginError = ''
      })
      const { domain, login, password } = params
      const { access_token, refresh_token, userDetail } = await api.userManager.login({
        login: `${login}@${domain}`,
        password,
      })
      if (access_token) {
        setTokensAndInformation({
          token: access_token,
          refreshToken: refresh_token,
          userDetail,
        })
        runInAction(() => {
          this.isAuthProgress = false
          this.userDetail = userDetail
          this.isAuth = true
        })

        return this.initialUrl
      }

      return false
    } catch (e: unknown) {
      if (e instanceof Error) {
        this.rootStore.notificationStore.addNotification({
          title: 'Ошибка',
          description: e.message,
          type: 'error',
        })
      }

      return false
    } finally {
      runInAction(() => {
        this.isAuthProgress = false
      })
    }
  }

  logout = async () => {
    try {
      await api.userManager.logout()
      clearTokensAndInformation()
      runInAction(() => {
        this.userDetail = { roles: [], initials: '' }
        this.isAuth = false
      })
    } catch (e) {
      console.log('e', e)
    }
  }

  checkAuth = async () => {
    try {
      const isLoginPage = window.location.pathname === '/login'
      if (!isLoginPage) {
        const { token } = getTokensAndInformation()
        const isValidToken = checkTokenTime(token)
        if (token && isValidToken) {
          // console.log("CONNECT WITH AUTH")
          connect()
          const { department, departmentId, initials, login, name, roles } = await api.userManager.getInfoUser()
          runInAction(() => {
            // this.userDetail = userDetail
            this.userDetail = {
              department,
              departmentId,
              initials,
              login,
              name,
              roles,
            }
            this.isAuth = true
          })
        } else {
          // disconnect()
          // console.log("CONNECT WITHOUT AUTH")
          await updateToken().then(() => {
            connect()
          })
          const { department, departmentId, initials, login, name, roles } = await api.userManager.getInfoUser()
          runInAction(() => {
            this.userDetail = {
              department,
              departmentId,
              initials,
              login,
              name,
              roles,
            }
            this.isAuth = true
          })
        }
      }
    } catch (e) {
      console.log(e)
      this.isAuth = false
    }
  }

  get initialUrl() {
    const initialUrlByRoleAndPriority = [
      { role: 'TECHNOLOGIST', url: '/calculations' },
      { role: 'TECH_ADMIN_CM', url: '/calcModel' },
      { role: 'TECH_ADMIN_NSI', url: '/nsi' },
      { role: 'ADMIN', url: '/administration' },
      { role: 'GUEST', url: '/calculations' },
    ]

    for (const item of initialUrlByRoleAndPriority) {
      if (this?.userDetail?.roles?.some((role: IUserRole) => role.role === item.role)) {
        return item.url
      }
    }

    return '/nsi'
  }
}

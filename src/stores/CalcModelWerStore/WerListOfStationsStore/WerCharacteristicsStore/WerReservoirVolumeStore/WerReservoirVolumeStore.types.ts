import {
  CharacteristicsSpreadsheetCell,
  IReservoirVolumeTableWithSettingsResponse,
  IReservoirVolumeVbLevelIndicatorDto,
} from 'entities/api/calcModelWerManager.entities'
import Handsontable from 'handsontable'
import { EditorType } from 'handsontable/editors'
import { ItemsProps } from 'shared/ui/Select/Select'
import { RootStore } from 'stores/RootStore'
import { SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib'

type GridSettings = Handsontable.GridSettings

interface ReservoirVolumeSpreadsheetColumnProps {
  readonly: boolean
  editor: EditorType | boolean
}

interface ReservoirVolumeSpreadsheetCellProps extends CharacteristicsSpreadsheetCell {
  className: string
  message?: string
}

type ReservoirVolumeSpreadsheetProps = SpreadsheetBaseProps<
  ReservoirVolumeSpreadsheetColumnProps,
  ReservoirVolumeSpreadsheetCellProps
>

export interface IWerReservoirVolumeStore {
  rootStore: RootStore
  initialSettings: IReservoirVolumeTableWithSettingsResponse['settings'] | null
  currentSettings: IReservoirVolumeTableWithSettingsResponse['settings'] | null
  isSettingsModified: boolean
  isSettingsSaving: boolean
  isCharacteristicsLoaded: boolean
  lastFilledRow: IReservoirVolumeTableWithSettingsResponse['lastFilledRow'] | null
  characteristicsDataSpreadsheet: ReservoirVolumeSpreadsheetProps
  originalCharacteristicsDataSpreadsheet: ReservoirVolumeSpreadsheetProps
  isEditRows: boolean
  vbLevelIndicators: ItemsProps[]
  currentVbLevelIndicator: IReservoirVolumeVbLevelIndicatorDto | null

  resetStore: () => void
  getReservoirVolumeCharacteristics: (plantId: number, date: string) => Promise<void>
  saveReservoirVolumeSettings: (
    plantId: number,
    date: string,
    settings: IReservoirVolumeTableWithSettingsResponse['settings'],
  ) => Promise<void>
  updateCharacteristicsSpreadsheetData: GridSettings['afterChange']
  saveChangedCharacteristicsSpreadsheetData: () => Promise<void>
  resetCharacteristicsSpreadsheetData: () => void
  resetSettings: () => void
  setRowCount: (rowCount: number | string) => void
  setUnit: (unit: IReservoirVolumeTableWithSettingsResponse['settings']['unit']) => void
  setLevelAllowableChange: (levelAllowableChange: number | string) => void
  setVolumeAllowableChange: (volumeAllowableChange: number | string) => void
  setEmptyValuesValidation: (emptyValuesValidation: boolean) => void
  setAllowableChangeValidation: (allowableChangeValidation: boolean) => void
  setMonotonyValidation: (monotonyValidation: boolean) => void
  setPolynom: (index: number, value: number | string) => void
  setMethod: (method: IReservoirVolumeTableWithSettingsResponse['settings']['method']) => void
  getReservoirVolumeIndicatorVbLevel: (plantId: number) => Promise<void>
  updateCurrentVbLevelIndicator: (id: string, plantId: number) => Promise<void>
}

import { IValidationSettings } from 'entities/api/calcModelWerManager.entities'

/**
 * Тип, описывающий значение ячейки.
 */
type DataValue = string | number | undefined

/**
 * Тип, описывающий допустимые изменения значений для каждого столбца
 * для валидации по правилу 2.
 *
 * Ключ – индекс столбца, а значение – допустимое изменение (дельта) для этого
 * столбца.
 */
type AllowableChanges = Record<number, DataValue>

/**
 * Преобразование всей матрицы данных за один проход.
 * Каждая ячейка, если пуста ('', null или undefined),
 * преобразуется в null, иначе – в число.
 */
const getParsedMatrix = (data: DataValue[][]): (number | null)[][] => {
  return data.map((row) =>
    row.map((cell) => {
      if (cell === '' || cell === null || cell === undefined) return null

      return Number(cell)
    }),
  )
}

/**
 * Функция для добавления сообщения об ошибке.
 *
 * @param errorMap - Map с ключом в формате "row:col" для хранения ошибок.
 * @param row - Индекс строки.
 * @param col - Индекс столбца.
 * @param message - Сообщение об ошибке.
 */
const addError = (errorMap: Map<string, Set<string>>, row: number, col: number, message: string): void => {
  const key = `${row}:${col}`
  const existingErrors = errorMap.get(key)
  if (existingErrors) {
    existingErrors.add(message)
  } else {
    errorMap.set(key, new Set([message]))
  }
}

/**
 * Находит индексы последних заполненных ячеек для каждого столбца
 */
const findLastFilledRowIndices = (parsedData: (number | null)[][], rowCount: number, colCount: number): number[] => {
  const lastFilledIndices: number[] = []

  for (let col = 0; col < colCount; col++) {
    let lastFilled = -1
    for (let row = rowCount - 1; row >= 0; row--) {
      if (parsedData[row][col] !== null) {
        lastFilled = row
        break
      }
    }
    lastFilledIndices[col] = lastFilled
  }

  return lastFilledIndices
}

/**
 * Правило 1.
 *
 * Если ячейка пустая, а предыдущая и следующая заполнены, выдаем ошибку.
 * Это правило проверяется для всех "средних" ячеек (т.е. если есть и предыдущая, и следующая).
 */
const validateRule1 = (
  parsedData: (number | null)[][],
  colCount: number,
  lastFilledIndices: number[],
  errorMap: Map<string, Set<string>>,
): void => {
  for (let col = 0; col < colCount; col++) {
    const lastFilled = lastFilledIndices[col]

    // Если нашли последнюю заполненную, проверяем пропуски до нее
    if (lastFilled > 0) {
      for (let row = 0; row < lastFilled; row++) {
        if (parsedData[row][col] === null) {
          addError(errorMap, row, col, 'Значение не должно быть пустым')
        }
      }
    }
  }
}

/**
 * Правило 2.
 *
 * Абсолютная разница между двумя соседними значениями
 * в одном столбце должна быть не больше limit и не равна нулю.
 *
 * limit - это величина, которая определяется в настройках характеристики
 * и зависит от индекса столбца (allowableChanges[col])
 */
const validateRule2 = (
  parsedData: (number | null)[][],
  colCount: number,
  allowableChanges: AllowableChanges,
  lastFilledIndices: number[],
  errorMap: Map<string, Set<string>>,
): void => {
  for (let col = 0; col < colCount; col++) {
    const rawLimit = allowableChanges[col]
    const limit = Number(rawLimit)
    const lastFilled = lastFilledIndices[col]

    // Проходим по парам соседних ячеек в столбце до последней заполненной
    for (let row = 1; row <= lastFilled; row++) {
      const prevNum = parsedData[row - 1][col]
      const currNum = parsedData[row][col]
      // Если оба значения определены (не null), то проверяем разницу.
      if (prevNum !== null && currNum !== null) {
        const diff = Math.abs(currNum - prevNum)
        // Округляем разницу до трех знаков после запятой, чтобы нивелировать
        // мелкие погрешности вычислений с плавающей точкой. Без этого округления
        // арифметические неточности могут приводить к ложным срабатываниям валидации.
        const roundedDiff = Math.round(diff * 1000) / 1000
        if (roundedDiff > limit || roundedDiff === 0) {
          addError(errorMap, row, col, `Разница между значениями должна быть > 0 и не превышать ${limit}`)
        }
      }
    }
  }
}

/**
 * Правило 3.
 *
 * Значения в обоих столбцах должны либо одновременно строго возрастать,
 * либо строго убывать.
 *
 * Логика проверки для набора из трёх последовательных строк (i-1, i, i+1):
 *
 * 1. Определяем направление изменений для первой пары:
 *    - trendFirst_0 = sign(parsedData[i][0] - parsedData[i-1][0])
 *    - trendFirst_1 = sign(parsedData[i][1] - parsedData[i-1][1])
 *
 *    Если направления в столбцах различаются, то ошибка ставится в строке i.
 *
 * 2. Если первая пара согласована (общий тренд),
 *    сравниваем его со знаком разницы для второй пары:
 *    - trendSecond_0 = sign(parsedData[i+1][0] - parsedData[i][0])
 *    - trendSecond_1 = sign(parsedData[i+1][1] - parsedData[i][1])
 *
 *    Если хотя бы для одного столбца знак отличается от установленного
 *    общего тренда, значит, нарушение произошло в строке i+1.
 */
const validateRule3 = (
  parsedData: (number | null)[][],
  colCount: number,
  lastFilledIndices: number[],
  errorMap: Map<string, Set<string>>,
): void => {
  if (colCount >= 2) {
    // Находим минимальную из последних заполненных ячеек для двух столбцов
    const minLastFilled = Math.min(lastFilledIndices[0], lastFilledIndices[1])

    // Нет смысла проверять, если нет достаточного количества строк
    if (minLastFilled < 2) return

    for (let row = 1; row < minLastFilled; row++) {
      const xPrev = parsedData[row - 1][0]
      const xCurr = parsedData[row][0]
      const xNext = parsedData[row + 1][0]

      const yPrev = parsedData[row - 1][1]
      const yCurr = parsedData[row][1]
      const yNext = parsedData[row + 1][1]

      // Если все три значения по обоим столбцам заданы:
      if (xPrev !== null && xCurr !== null && xNext !== null && yPrev !== null && yCurr !== null && yNext !== null) {
        // Направление изменений для первой пары
        const trendXFirst = Math.sign(xCurr - xPrev)
        const trendYFirst = Math.sign(yCurr - yPrev)

        // Если тренды для первой пары различаются,
        // значит уже в строке i наблюдается нарушение.
        if (trendXFirst !== trendYFirst) {
          addError(errorMap, row, 0, 'Значения должны монотонно возрастать или убывать')
          addError(errorMap, row, 1, 'Значения должны монотонно возрастать или убывать')
        } else {
          // Первая пара согласована, зафиксируем общий тренд
          const commonTrend = trendXFirst

          // Направление изменений для второй пары
          const trendXSecond = Math.sign(xNext - xCurr)
          const trendYSecond = Math.sign(yNext - yCurr)

          // Если хотя бы для одного столбца тренд во второй паре отличается
          // от общего тренда, ошибка ставится в строке (i+1)
          if (trendXSecond !== commonTrend || trendYSecond !== commonTrend) {
            addError(errorMap, row + 1, 0, 'Значения должны монотонно возрастать или убывать')
            addError(errorMap, row + 1, 1, 'Значения должны монотонно возрастать или убывать')
          }
        }
      }
    }
  }
}

// Преобразуем errorMap в Map<string, string>, объединяя уникальные сообщения через '\n'
const processErrorMap = (errorMap: Map<string, Set<string>>): Map<string, string> => {
  const processed = new Map<string, string>()
  errorMap.forEach((messages, key) => {
    processed.set(key, Array.from(messages).join('\n'))
  })

  return processed
}

/**
 * Функция для валидации всей матрицы данных таблицы.
 *
 * Параметры:
 * - data: матрица значений
 * - allowableChanges: объект, где ключ - индекс столбца (0 и 1), а значение - допустимое
 *   изменение (дельта) для этого столбца для правила 2.
 * - validationSettings: настройки включения/отключения правил валидации
 *
 * В этой версии мы сначала преобразуем всю матрицу за один проход,
 * чтобы уменьшить количество повторных преобразований значений ячеек.
 */
export const validateCharacteristicsTableData = (
  data: DataValue[][],
  allowableChanges: AllowableChanges,
  validationSettings: IValidationSettings = {},
): Map<string, string> => {
  // Map с ключом `row:col` и значением типа Set, который хранит уникальные сообщения
  const errorMap = new Map<string, Set<string>>()
  const rowCount = data.length
  const colCount = data[0]?.length ?? 0

  const parsedData = getParsedMatrix(data)
  const lastFilledIndices = findLastFilledRowIndices(parsedData, rowCount, colCount)

  // Применяем правила валидации только если они включены в настройках характеристики
  if (validationSettings.emptyValuesValidation) {
    validateRule1(parsedData, colCount, lastFilledIndices, errorMap)
  }

  if (validationSettings.allowableChangeValidation) {
    validateRule2(parsedData, colCount, allowableChanges, lastFilledIndices, errorMap)
  }

  if (validationSettings.monotonyValidation) {
    validateRule3(parsedData, colCount, lastFilledIndices, errorMap)
  }

  return processErrorMap(errorMap)
}

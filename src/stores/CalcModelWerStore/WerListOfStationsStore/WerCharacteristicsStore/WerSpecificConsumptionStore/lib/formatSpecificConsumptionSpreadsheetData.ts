import { CharacteristicsSpreadsheetCell, IValidationSettings } from 'entities/api/calcModelWerManager.entities'
import { IGetSpreadsheetData } from 'entities/shared/spreadsheetDataResponse'
import Handsontable from 'handsontable'
import { Editors } from 'handsontable/editors'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import { classNames } from 'shared/lib/classNames'
import { convertSpreadsheetResponseToComponentProps, SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib'

import { validateCharacteristicsTableData } from '../../lib'
import { IWerSpecificConsumptionStore } from '../WerSpecificConsumptionStore.types'
import { formatSpecificConsumptionNumericValue } from '.'

class CustomNumericEditor extends Handsontable.editors.NumericEditor {
  // Создаём именованный обработчик, чтобы его можно было впоследствии удалить
  inputHandler = () => {
    const input = this.TEXTAREA as HTMLInputElement
    // Получаем требуемое число знаков после запятой из cellProperties,
    // если оно не задано — по умолчанию берем 3 знака.
    const maxDecimals = this.cellProperties?.maxDecimals ?? 3
    input.value = formatSpecificConsumptionNumericValue(input.value, false, maxDecimals)
  }

  open() {
    super.open()
    const input = this.TEXTAREA as HTMLInputElement
    input.addEventListener('input', this.inputHandler)
  }

  close() {
    super.close()
    const input = this.TEXTAREA as HTMLInputElement
    input.removeEventListener('input', this.inputHandler)
  }

  getValue() {
    const inputValue = (this.TEXTAREA as HTMLInputElement).value
    /*
      Если пользователь оставил значение, начинающееся с точки (например, ".567"),
      добавляем ведущий ноль и возвращаем "0.567". Это гарантирует, что в ячейке будет
      корректное числовое представление.
    */
    if (inputValue.startsWith('.') && inputValue.length > 1) {
      return '0' + inputValue
    }

    return inputValue
  }
}

Handsontable.editors.registerEditor('SpecificConsumptionNumericEditor', CustomNumericEditor)

type GetCellStyle = (props: {
  cell: SpreadsheetBaseProps<NonNullable<unknown>, CharacteristicsSpreadsheetCell>['cell'][0]
  canEdit: boolean
  isSelectedDateEditable: boolean
  isSelectedPlantViewOnly: boolean
  message?: string
}) => string

export const getCellStyle: GetCellStyle = ({ canEdit, isSelectedDateEditable, isSelectedPlantViewOnly, message }) => {
  return classNames(
    'htCenter',
    { ['isNotValid']: !!message, ['disabledCell']: !canEdit || !isSelectedDateEditable || isSelectedPlantViewOnly },
    [],
  )
}

export const formatSpecificConsumptionSpreadsheetData = (
  spreadsheetData: IGetSpreadsheetData<CharacteristicsSpreadsheetCell>,
  canEdit: boolean,
  isSelectedDateEditable: boolean,
  isSelectedPlantViewOnly: boolean,
  pressureAllowableChange?: number | string,
  consumptionAllowableChange?: number | string,
  validationSettings: IValidationSettings = {},
): IWerSpecificConsumptionStore['specificConsumptionSpreadsheetData'] => {
  const spreadsheetProps = convertSpreadsheetResponseToComponentProps<CharacteristicsSpreadsheetCell>(spreadsheetData)

  // Выполняем валидацию исходной матрицы данных
  const errorsMap = validateCharacteristicsTableData(
    spreadsheetProps.data,
    {
      0: pressureAllowableChange,
      1: consumptionAllowableChange,
    },
    validationSettings,
  )

  return {
    ...spreadsheetProps,
    columns: spreadsheetProps.columns.map((_, index) => ({
      readonly: !canEdit || !isSelectedDateEditable || isSelectedPlantViewOnly,
      editor: 'SpecificConsumptionNumericEditor' as keyof Editors,
      maxDecimals: index === 0 ? 1 : 3,
    })),
    cell: spreadsheetProps.cell.map((cell) => {
      // Получаем ошибку для ячейки по ключу
      const cellKey = `${cell.row}:${cell.col}`
      const message = errorsMap.get(cellKey)
      const className = getCellStyle({
        cell,
        canEdit,
        isSelectedDateEditable,
        isSelectedPlantViewOnly,
        message,
      })

      return {
        row: cell.row,
        col: cell.col,
        className,
        editor: 'SpecificConsumptionNumericEditor',
        readOnly: !canEdit || !isSelectedDateEditable || isSelectedPlantViewOnly,
        value: cell.value,
        column: cell.column,
        comment: message ? getStyledComment(message) : undefined,
        message: message, // Сохраняем для сравнения при редактировании ячейки
      }
    }),
  }
}

import {
  CharacteristicsSpreadsheet<PERSON>ell,
  IDownstreamPlant,
  ITailraceTableWithSettingsResponse,
} from 'entities/api/calcModelWerManager.entities'
import Handsontable from 'handsontable'
import { EditorType } from 'handsontable/editors'
import { RootStore } from 'stores/RootStore'
import { SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib'

type GridSettings = Handsontable.GridSettings

interface TailraceSpreadsheetColumnProps {
  readonly: boolean
  editor: EditorType | boolean
}

interface CharacteristicsSpreadsheetCellProps extends CharacteristicsSpreadsheetCell {
  className: string
  message?: string
}

type TailraceSpreadsheetProps = SpreadsheetBaseProps<
  TailraceSpreadsheetColumnProps,
  CharacteristicsSpreadsheetCellProps
>

interface ChartPoint {
  readonly x: number
  readonly y: number
}

export interface IWerTailraceStore {
  rootStore: RootStore
  initialSettings: ITailraceTableWithSettingsResponse['settings'] | null
  currentSettings: ITailraceTableWithSettingsResponse['settings'] | null
  isSettingsModified: boolean
  isSettingsSaving: boolean
  isCharacteristicsLoaded: boolean
  isCharacteristicsSaving: boolean
  downstreamPlantList: IDownstreamPlant[]
  selectedDownstreamPlant: IDownstreamPlant | undefined
  lastFilledRow: ITailraceTableWithSettingsResponse['lastFilledRow'] | null
  tailraceSpreadsheetData: TailraceSpreadsheetProps
  originalTailraceSpreadsheetData: TailraceSpreadsheetProps
  isEditRows: boolean
  isFreezing: boolean
  freezingLastFilledRow: ITailraceTableWithSettingsResponse['freezingLastFilledRow'] | null
  freezingTailraceSpreadsheetData: TailraceSpreadsheetProps
  originalFreezingTailraceSpreadsheetData: TailraceSpreadsheetProps
  actualData: number[][]
  isActualDataLoading: boolean

  resetStore: () => void

  _getDownstreamPlantList: (plantId: number, date: string) => Promise<void>

  setSelectedDownstreamPlant: (downstreamPlant?: IDownstreamPlant) => void
  setDownstreamPlantLevel: (index: number, value: number | string) => void
  addDownstreamPlantLevel: () => void
  deleteDownstreamPlantLevel: (index: number) => void

  getTailraceCharacteristics: (plantId: number, date: string, downstreamPlant?: IDownstreamPlant) => Promise<void>
  saveTailraceSettings: (
    plantId: number,
    date: string,
    settings: ITailraceTableWithSettingsResponse['settings'],
  ) => Promise<void>
  updateCharacteristicsSpreadsheetData: GridSettings['afterChange']
  saveChangedCharacteristicsSpreadsheetData: () => Promise<void>
  resetCharacteristicsSpreadsheetData: () => void

  freezing: boolean | undefined
  toggleFreezing: () => void

  resetSettings: () => void

  getActualData: (
    plantId: number,
    date: string,
    startDate: string,
    endDate: string,
    signal?: AbortSignal,
  ) => Promise<void>
  seriesActualData: ChartPoint[]

  setRowCount: (rowCount: number | string) => void
  setFreezingWatch: (isFreezingWatch: boolean) => void
  setFreezingDate: (dateType: 'freezingBegin' | 'freezingEnd', date: Date) => void
  setTailraceAllowableChange: (tailraceAllowableChange: number | string) => void
  setConsumptionAllowableChange: (consumptionAllowableChange: number | string) => void
  setEmptyValuesValidation: (emptyValuesValidation: boolean) => void
  setAllowableChangeValidation: (allowableChangeValidation: boolean) => void
  setMonotonyValidation: (monotonyValidation: boolean) => void
  setPolynom: (index: number, value: number | string) => void
  setMethod: (method: ITailraceTableWithSettingsResponse['settings']['method']) => void
}

import { format } from 'date-fns'
import { IAVRCHMSpreadsheetNestedHeaderData, IGetAVRCHMSpreadsheetCell } from 'entities/api/calcModelPage.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { IAvrchmSpreadsheetSelectedCell, IAvrchmStore } from 'entities/store/avrchmStore.entities.ts'
import { ICalculationsPageStore } from 'entities/store/calculationPageStore.entities.ts'
import Handsontable from 'handsontable'
import { makeAutoObservable, runInAction, toJS } from 'mobx'
import { ACTUAL_ITEM, getStatusCell, getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import api from 'shared/api'
import { IVaultStore } from 'stores/CalculationsPageStore/VaultStore'
import { type RootStore } from 'stores/RootStore'
import {
  convertSpreadsheetResponseToComponentProps,
  getSpreadsheetSelectedCells,
  SpreadsheetSelectedCells,
  validateAvrchmNorm,
} from 'widgets/Spreadsheet/ui/lib'

const initAvrchmSpreadsheet = {
  columns: [],
  data: [],
  nestedHeaders: [],
  cell: [],
  rowHeaders: [],
}

export class AvrchmStore {
  rootStore: ICalculationsPageStore['rootStore']
  originalAvrchmSpreadsheet: IAvrchmStore['originalAvrchmSpreadsheet'] = initAvrchmSpreadsheet
  avrchmSpreadsheet: IAvrchmStore['avrchmSpreadsheet'] = initAvrchmSpreadsheet
  avrchmSpreadsheetDataLoaded: IAvrchmStore['avrchmSpreadsheetDataLoaded'] = false
  isChangedAvrchmSpreadsheet: IAvrchmStore['isChangedAvrchmSpreadsheet'] = false
  selectedCellsBeforeFix: IAvrchmStore['selectedCellsBeforeFix'] = []
  avrchmColumns: IAvrchmStore['avrchmColumns'] = []
  changedAvrchmCells: IAvrchmStore['changedAvrchmCells'] = []
  isInitAvrchmSpreadsheet: IAvrchmStore['isInitAvrchmSpreadsheet'] = false

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  get canEdit() {
    return this.rootStore.authStore.userDetail.roles
      .map((el) => el.role)
      .some((el: string) => {
        return [ROLES.TECHNOLOGIST].some((item) => item === el)
      })
  }

  private get _vaultStore() {
    return this.rootStore.calculationsPageStore.vaultStore
  }

  private _getAvrchmSpreadsheetData: IAvrchmStore['_getAvrchmSpreadsheetData'] = async (
    calcDate,
    planingStage,
    shouldChangeRequestStatus,
  ) => {
    try {
      if (!this.isInitAvrchmSpreadsheet || shouldChangeRequestStatus) {
        this.avrchmSpreadsheetDataLoaded = false
      }
      const avrchmSpreadsheetData = await api.calculationsManager.getCalculationSummaryAvrchm(calcDate, planingStage)
      const spreadsheetProps = convertSpreadsheetResponseToComponentProps<
        IGetAVRCHMSpreadsheetCell,
        IAVRCHMSpreadsheetNestedHeaderData
      >(avrchmSpreadsheetData)
      const addedPlantIds: number[] = []
      const plantsIds = this._vaultStore.plantsData.map((plant) => plant.plantId)
      const data = spreadsheetProps.data.map((rowData, row) =>
        rowData.map((itemForHour, col) => {
          const cellIdx = row * spreadsheetProps.columns.length + col
          const cell = spreadsheetProps.cell[cellIdx]
          if (cell?.plantId && plantsIds.includes(cell.plantId)) {
            return itemForHour ? itemForHour : 0
          }

          return itemForHour
        }),
      )
      runInAction(() => {
        this.avrchmSpreadsheet = {
          ...spreadsheetProps,
          data,
          columns: spreadsheetProps.columns.map(() => ({
            readonly: false,
            editor: 'numeric',
          })),
          cell: spreadsheetProps.cell.map((cell) => {
            const newCell = {
              row: cell.row,
              col: cell.col,
              className: '',
              editable: cell?.editable,
              editor: cell?.editable && !cell?.floodMode ? 'AVRCHMNumericEditor' : false,
              readOnly: !this.canEdit || !cell?.editable || cell?.floodMode,
              value: data[cell.row][cell.col],
              plantId: cell?.plantId,
              manual: cell?.manual,
              fixed: cell?.fixed,
              floodMode: cell?.floodMode,
              minNorm: cell?.minNorm,
              comment: {
                value: '',
              },
            }
            if (cell?.plantId !== undefined && !addedPlantIds.includes(cell?.plantId)) {
              this.avrchmColumns = [...this.avrchmColumns, { columnIdx: cell.col, plantId: cell.plantId }]
              addedPlantIds.push(cell.plantId)
            }

            return {
              ...newCell,
              ...this._validateAVRCHMCell(newCell),
            }
          }),
        }
        this.originalAvrchmSpreadsheet = toJS(this.avrchmSpreadsheet)
        this.isChangedAvrchmSpreadsheet = false
        this.isInitAvrchmSpreadsheet = true
      })
    } catch (e) {
      console.log(e)
    } finally {
      runInAction(() => {
        this.avrchmSpreadsheetDataLoaded = true
      })
    }
  }

  private _validateAVRCHMCell: IAvrchmStore['_validateAVRCHMCell'] = (cell) => {
    const vaultAVRCHMColumn = this._vaultStore.avrchmColumns.find((column) => column.plantId === cell.plantId)
    let vaultSpreadsheetCell: IVaultStore['vaultSpreadsheet']['cell'][0] | undefined
    if (vaultAVRCHMColumn) {
      const vaultCellIdx = cell.row * this._vaultStore.vaultSpreadsheet.column.length + vaultAVRCHMColumn?.columnIdx
      vaultSpreadsheetCell = this._vaultStore.vaultSpreadsheet.cell[vaultCellIdx]
    }
    const comments = []
    const spreadsheetComment = vaultSpreadsheetCell?.comment?.value ? vaultSpreadsheetCell.comment.value : ''
    if (spreadsheetComment) {
      comments.push(spreadsheetComment)
    }
    const avrchmValidationComment = validateAvrchmNorm(cell?.value, cell.minNorm)
    if (avrchmValidationComment) {
      comments.push(avrchmValidationComment)
    }
    const comment = comments.join('\n')
    const isValid = comments.length === 0

    const className = getStatusCell(
      false,
      false,
      cell?.editor && this.canEdit ? 'numeric' : false,
      '', // Не используется для АВРЧМ
      '', // Не используется для АВРЧМ
      isValid,
      !!cell?.manual || !!cell?.fixed,
    )

    return {
      className,
      comment: getStyledComment(comment),
    }
  }

  resetStore = () => {
    this.originalAvrchmSpreadsheet = initAvrchmSpreadsheet
    this.avrchmSpreadsheet = initAvrchmSpreadsheet
    this.avrchmSpreadsheetDataLoaded = false
    this.isChangedAvrchmSpreadsheet = false
    this.selectedCellsBeforeFix = []
    this.avrchmColumns = []
    this.changedAvrchmCells = []
    this.isInitAvrchmSpreadsheet = false
  }

  initAvrchm: IAvrchmStore['initAvrchm'] = async (shouldChangeRequestStatus = false) => {
    const calculationsPageStore = this.rootStore.calculationsPageStore
    const selectedStage = calculationsPageStore.selectedStage
    const actualStage = calculationsPageStore.actualStage
    const date = calculationsPageStore.date

    const planingStage = (selectedStage === ACTUAL_ITEM.value ? actualStage?.code : selectedStage) as string
    await this._getAvrchmSpreadsheetData(format(date, 'yyyy-MM-dd'), planingStage, shouldChangeRequestStatus)
  }

  resetAvrchm: IAvrchmStore['resetAvrchm'] = () => {
    runInAction(() => {
      this.avrchmSpreadsheet = toJS(this.originalAvrchmSpreadsheet)
      this.isChangedAvrchmSpreadsheet = false
      this.changedAvrchmCells = []
      this.selectedCellsBeforeFix = []
    })
  }

  handleAfterChange: IAvrchmStore['handleAfterChange'] = (changes) => {
    const availableChanges = changes?.filter(([, , prevValue, nextValue]) => Number(prevValue) !== Number(nextValue))

    if (!availableChanges || !this.avrchmSpreadsheet.data.length) return

    const newAVRCHMData = toJS(this.avrchmSpreadsheet.data)
    const newAVRHCMCell = toJS(this.avrchmSpreadsheet.cell)
    const changedCellIdxs: number[] = []

    availableChanges.forEach((change) => {
      const [changedRow, changedColumn, _, newValue] = change
      const notEmptyValue = newValue !== '' && newValue !== null
      newAVRCHMData[changedRow][Number(changedColumn)] = notEmptyValue ? Number(newValue) : null

      const cellIdx = changedRow * this.avrchmSpreadsheet.columns.length + Number(changedColumn)
      newAVRHCMCell[cellIdx].manual = notEmptyValue
      newAVRHCMCell[cellIdx].fixed = notEmptyValue ? newAVRHCMCell[cellIdx]?.fixed : notEmptyValue
      newAVRHCMCell[cellIdx].value = newAVRCHMData[changedRow][Number(changedColumn)]
      changedCellIdxs.push(cellIdx)
    })

    const newVaultCell = [...this._vaultStore.vaultSpreadsheet.cell]
    const newVaultData = [...this._vaultStore.vaultSpreadsheet.data]
    const newEditCellsUp = [...this._vaultStore.editCellsUp]
    const changesUp: Handsontable.CellChange[] = []
    changedCellIdxs.forEach((idx) => {
      const cellIdx = newVaultCell.findIndex(
        (cellUpData) =>
          cellUpData?.keyStation === 'AVRCHM_LOAD' &&
          cellUpData?.idStation === newAVRHCMCell[idx].plantId &&
          cellUpData.row === newAVRHCMCell[idx].row,
      )
      if (cellIdx !== -1) {
        const notEmptyValue = newAVRHCMCell[idx].value !== '' && newAVRHCMCell[idx].value !== null
        newVaultCell[cellIdx]['manual'] = notEmptyValue
        newVaultCell[cellIdx]['fixed'] = notEmptyValue ? newAVRHCMCell[cellIdx]?.fixed : notEmptyValue
        // очищаем допустимые зоны для станции
        newVaultCell.forEach((cell) => {
          if (cell.row === newAVRHCMCell[idx].row && cell.idStation === newAVRHCMCell[idx].plantId) {
            cell['allowedZones'] = []
          }
        })
        newVaultData[newAVRHCMCell[idx].row][newVaultCell[cellIdx].col] = newAVRHCMCell[idx].value
        newEditCellsUp.push(
          `${newVaultCell[cellIdx]['row']}-${newVaultCell[cellIdx]['col']}-${newAVRHCMCell[idx].plantId}`,
        )
        changesUp.push([
          newVaultCell[cellIdx]['row'],
          newVaultCell[cellIdx]['col'],
          this.avrchmSpreadsheet.cell[idx].value,
          newAVRHCMCell[idx].value,
        ])
      }
    })

    // Сохраняем данные по таблице Свода и валидируем их
    runInAction(() => {
      this._vaultStore.vaultSpreadsheet = {
        ...toJS(this.rootStore.calculationsPageStore.vaultStore.vaultSpreadsheet),
        data: newVaultData,
        cell: newVaultCell,
      }
      this._vaultStore.validate(newVaultData, changesUp)
      this._vaultStore.editCellsUp = newEditCellsUp
    })

    // Комбинируем валидацию со Свода и АВРЧМ
    changedCellIdxs.forEach((cellIdx) => {
      const validateData = this._validateAVRCHMCell(newAVRHCMCell[cellIdx])
      newAVRHCMCell[cellIdx].className = validateData.className
      newAVRHCMCell[cellIdx].comment = validateData.comment
    })

    // Сохраняем данные по таблице АВРЧМ
    runInAction(() => {
      this.changedAvrchmCells = [...new Set([...toJS(this.changedAvrchmCells), ...changedCellIdxs])]
      this.isChangedAvrchmSpreadsheet = true
      this.avrchmSpreadsheet = {
        ...toJS(this.avrchmSpreadsheet),
        data: newAVRCHMData,
        cell: newAVRHCMCell,
      }
    })
  }

  syncCellChangeWithVault: IAvrchmStore['syncWithVault'] = (changes) => {
    let cell = this.avrchmSpreadsheet.cell
    changes.forEach(([change, plantId]) => {
      const [changedRow, , , newValue] = change
      cell = [
        ...cell.map(({ row, col, ...rest }) => {
          let newCell = {
            row,
            col,
            ...rest,
          }
          if (row === changedRow && plantId === rest.plantId && newValue !== undefined) {
            const notEmptyValue = newValue !== '' && newValue !== null
            newCell = {
              row,
              col,
              ...rest,
              fixed: notEmptyValue ? rest?.fixed : false,
              manual: notEmptyValue,
              value: notEmptyValue ? Number(newValue) : undefined,
            }
          }

          return {
            ...newCell,
            ...this._validateAVRCHMCell(newCell),
          }
        }),
      ]
    })

    runInAction(() => {
      this.avrchmSpreadsheet = {
        ...this.avrchmSpreadsheet,
        cell,
        data: cell.reduce((acc, item) => {
          if (acc[item.row] === undefined) {
            acc[item.row] = []
          }
          acc[item.row].push(item?.value)

          return acc
        }, [] as Handsontable.CellValue[][]),
      }
      this.isChangedAvrchmSpreadsheet = true
    })
  }

  syncVaultSelectionWithAvrchm: IAvrchmStore['syncVaultSelectionWithAvrchm'] = (changedCells) => {
    const cell = toJS(this.avrchmSpreadsheet.cell)
    changedCells
      .filter((changedCell) => changedCell?.idStation !== undefined)
      .forEach((changedCell) => {
        const avrchmColumn = this.avrchmColumns.find(
          (avrchmColumn) => avrchmColumn.plantId === changedCell.idStation && changedCell?.keyStation === 'AVRCHM_LOAD',
        )
        if (avrchmColumn) {
          const cellIdx = changedCell.row * this.avrchmSpreadsheet.columns.length + avrchmColumn?.columnIdx
          cell[cellIdx].fixed = changedCell.fixed
          if (!changedCell.fixed) {
            cell[cellIdx].manual = changedCell.fixed
          }
          const validateData = this._validateAVRCHMCell(cell[cellIdx])
          cell[cellIdx].className = validateData.className
          cell[cellIdx].comment = validateData.comment
        }
      })
    runInAction(() => {
      this.avrchmSpreadsheet = {
        ...toJS(this.avrchmSpreadsheet),
        cell,
      }
      this.isChangedAvrchmSpreadsheet = true
    })
  }

  selectSpreadsheetCoords: IAvrchmStore['selectSpreadsheetCoords'] = (hot) => {
    const selectedAVRCHMCells = getSpreadsheetSelectedCells(hot?.hotInstance)
    const transformedSelectedAVRCHMCells: IAvrchmSpreadsheetSelectedCell[] = []
    selectedAVRCHMCells.forEach((selectedCell) => {
      const { row, col, row2, col2 } = selectedCell
      for (let x = row; x <= row2; x++) {
        for (let y = col; y <= col2; y++) {
          const cellIdx = x * this.avrchmSpreadsheet.columns.length + y
          const cell = this.avrchmSpreadsheet.cell[cellIdx]
          if (cell?.editor === 'AVRCHMNumericEditor' && cell?.plantId !== undefined) {
            transformedSelectedAVRCHMCells.push({
              row: x,
              col: y,
              plantId: cell.plantId,
            })
          }
        }
      }
    })

    if (this._vaultStore.vaultSpreadsheet.nestedHeaders[4] !== undefined) {
      const newSelectedCellsBeforeFix: IAvrchmStore['selectedCellsBeforeFix'] = []
      const selectedCellsInVaultFormat: SpreadsheetSelectedCells[] = []
      transformedSelectedAVRCHMCells.forEach((selectedCell) => {
        const avrchmColumn = this._vaultStore.avrchmColumns.find(
          (avrchmColumn) => avrchmColumn.plantId === selectedCell.plantId,
        )
        if (avrchmColumn?.columnIdx !== undefined) {
          selectedCellsInVaultFormat.push({
            row: selectedCell.row,
            col: Number(avrchmColumn.columnIdx),
            row2: selectedCell.row,
            col2: Number(avrchmColumn.columnIdx),
          })
        } else {
          newSelectedCellsBeforeFix.push(selectedCell)
        }
      })
      runInAction(() => {
        this.selectedCellsBeforeFix = newSelectedCellsBeforeFix
        this._vaultStore.setSelectedCellsBeforeFix(selectedCellsInVaultFormat)
      })
    }
  }

  applySelectedCellChanges: IAvrchmStore['applySelectedCellChanges'] = (fixed) => {
    const newCell = toJS(this.avrchmSpreadsheet.cell)
    const changedCellIds: number[] = []
    this.selectedCellsBeforeFix.forEach((selectedCell) => {
      const cellIdx = selectedCell.row * this.avrchmSpreadsheet.columns.length + selectedCell.col
      newCell[cellIdx].fixed = fixed
      newCell[cellIdx].manual = !fixed ? fixed : newCell[cellIdx].manual
      const validateData = this._validateAVRCHMCell(newCell[cellIdx])
      newCell[cellIdx].className = validateData.className
      newCell[cellIdx].comment = validateData.comment
      changedCellIds.push(cellIdx)
    })
    runInAction(() => {
      this.avrchmSpreadsheet = {
        ...toJS(this.avrchmSpreadsheet),
        cell: newCell,
      }
      this.selectedCellsBeforeFix = []
      this.isChangedAvrchmSpreadsheet = true
      this.changedAvrchmCells = [...new Set([...toJS(this.changedAvrchmCells), ...changedCellIds])]
    })
  }

  changeFloodModeForAvrchmByPlantId: IAvrchmStore['changeFloodModeForAvrchmByPlantId'] = (plantId, enable) => {
    const coords: Handsontable.GridSettings['cell'] = []
    const newCell = toJS(this.avrchmSpreadsheet.cell).map((cell) => {
      if (cell?.plantId === plantId) {
        const newCell = {
          ...cell,
          floodMode: enable,
          manual: enable ? false : cell.manual,
          fixed: enable ? false : cell.fixed,
          editor: enable ? false : cell?.editable ? 'AVRCHMNumericEditor' : false,
          readOnly: enable ? true : !cell?.editable,
          value: enable ? 0 : cell.value,
        }
        coords.push({
          row: newCell.row,
          col: newCell.col,
        })

        return {
          ...newCell,
          ...this._validateAVRCHMCell(newCell),
        }
      }

      return cell
    })
    const newData = toJS(this.avrchmSpreadsheet.data)
    coords.forEach((coord) => {
      newData[coord.row][coord.col] = enable ? 0 : newData[coord.row][coord.col]
    })
    runInAction(() => {
      this.avrchmSpreadsheet = {
        ...toJS(this.avrchmSpreadsheet),
        cell: newCell,
        data: newData,
      }
    })
  }

  // При нажатии вне области таблицы необходимо сбрасывать состояние по выбранным ячейка для установки/снятия фиксации
  // при этом, если нажата кнопка установить/снять фиксацию, то выполнится соответствующй обработчик и значения не должны быть сброшены
  handleClickAway: IAvrchmStore['handleClickAway'] = (event) => {
    const fixingElementId = (event.target as HTMLElement).getAttribute('id')
    if (fixingElementId !== 'closeLock' && fixingElementId !== 'openLock') {
      runInAction(() => {
        this.selectedCellsBeforeFix = []
        this._vaultStore.selectedCellsBeforeFix = []
      })
    }
  }
}

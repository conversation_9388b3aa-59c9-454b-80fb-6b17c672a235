import { ICalculationSummaryItem } from 'entities/api/calculationsManager.entities.ts'
import { IPlantForLeftMenu } from 'stores/CalcModelStore'

const RESULT_COLUMNS_LENGTH = 3
const RESERVE_COLUMNS_LENGTH = 2
const LIMIT_COLUMNS_LENGTH = 2
const CM_COLUMNS_LENGTH = 2
const MODES_COLUMNS_LENGTH = 3

const keysGES = [
  'P_MIN_RESULT',
  'P_GEN',
  'P_MAX_RESULT',
  'RESERVES_MAX',
  'AVRCHM_LOAD',
  'P_MIN',
  'P_MAX',
  'CM_P_MIN',
  'CM_P_MAX',
  'MODES_P_MIN',
  'MODES_P_MAX',
  'MODES_DECLARED',
]

type HeaderKeyIndexMap = Record<string, number>

const getKey = (key: string, id1?: number, id2?: number) => {
  return (gesKey: string) => {
    return `${key}-${id1}-${gesKey}-${id2}`
  }
}

/**
 * Метод для генерации объекта, где ключами являются данные о колонке [`RGU-rguId-MODES-plantId`], а значениями индекс колонки
 * @param plantsForLeftMenu - Список станций в боковом меню, отфильтрованный по станциям доступных для просмотра на Своде
 * @param plantsData - Список данных по станциям, отфильтрованный по станциям доступных для просмотра на Своде
 */
export const generateHeaderKeyIndexMap = (
  plantsForLeftMenu: IPlantForLeftMenu[],
  plantsData: ICalculationSummaryItem[],
) =>
  plantsForLeftMenu.reduce((headerKeyIndexMap, plantForLeftMenu) => {
    const plantId = plantForLeftMenu.value
    const plantRgus = plantsData.find((ps) => ps?.plantId === plantId)?.rgus ?? []
    const firstRgu: HeaderKeyIndexMap = {}
    const secondRgu: HeaderKeyIndexMap = {}
    const thirdRgu: HeaderKeyIndexMap = {}
    const fourthRgu: HeaderKeyIndexMap = {}
    const fifthRgu: HeaderKeyIndexMap = {}

    const alreadyInitiatedColumnsLength = Object.keys(headerKeyIndexMap).length
    let resultRguColumnIdx = alreadyInitiatedColumnsLength + RESULT_COLUMNS_LENGTH

    let reserveRguColumnIdx = resultRguColumnIdx + RESULT_COLUMNS_LENGTH * plantRgus.length + RESERVE_COLUMNS_LENGTH

    let limitRguColumnIdx = reserveRguColumnIdx + RESERVE_COLUMNS_LENGTH * plantRgus.length + LIMIT_COLUMNS_LENGTH

    let cmRguColumnIdx = limitRguColumnIdx + LIMIT_COLUMNS_LENGTH * plantRgus.length + CM_COLUMNS_LENGTH

    let modesRguColumnIdx = cmRguColumnIdx + CM_COLUMNS_LENGTH * plantRgus.length + MODES_COLUMNS_LENGTH

    const getPlantKey = getKey('PLANT', plantId, plantId)
    plantRgus.forEach((rgu) => {
      const getRguKey = getKey('RGU', rgu?.rguId, plantId)
      firstRgu[getRguKey(keysGES[0])] = resultRguColumnIdx
      firstRgu[getRguKey(keysGES[1])] = resultRguColumnIdx + 1
      firstRgu[getRguKey(keysGES[2])] = resultRguColumnIdx + 2
      resultRguColumnIdx += 3

      secondRgu[getRguKey(keysGES[3])] = reserveRguColumnIdx
      secondRgu[getRguKey(keysGES[4])] = reserveRguColumnIdx + 1
      reserveRguColumnIdx += 2

      thirdRgu[getRguKey(keysGES[5])] = limitRguColumnIdx
      thirdRgu[getRguKey(keysGES[6])] = limitRguColumnIdx + 1
      limitRguColumnIdx += 2

      fourthRgu[getRguKey(keysGES[7])] = cmRguColumnIdx
      fourthRgu[getRguKey(keysGES[8])] = cmRguColumnIdx + 1
      cmRguColumnIdx += 2

      fifthRgu[getRguKey(keysGES[9])] = modesRguColumnIdx
      fifthRgu[getRguKey(keysGES[10])] = modesRguColumnIdx + 1
      fifthRgu[getRguKey(keysGES[11])] = modesRguColumnIdx + 2
      modesRguColumnIdx += 3
    })

    headerKeyIndexMap = {
      ...headerKeyIndexMap,
      [getPlantKey(keysGES[0])]: alreadyInitiatedColumnsLength,
      [getPlantKey(keysGES[1])]: alreadyInitiatedColumnsLength + 1,
      [getPlantKey(keysGES[2])]: alreadyInitiatedColumnsLength + 2,
      ...firstRgu,
      [getPlantKey(keysGES[3])]: resultRguColumnIdx,
      [getPlantKey(keysGES[4])]: resultRguColumnIdx + 1,
      ...secondRgu,
      [getPlantKey(keysGES[5])]: reserveRguColumnIdx,
      [getPlantKey(keysGES[6])]: reserveRguColumnIdx + 1,
      ...thirdRgu,
      [getPlantKey(keysGES[7])]: limitRguColumnIdx,
      [getPlantKey(keysGES[8])]: limitRguColumnIdx + 1,
      ...fourthRgu,
      [getPlantKey(keysGES[9])]: cmRguColumnIdx,
      [getPlantKey(keysGES[10])]: cmRguColumnIdx + 1,
      ...fifthRgu,
    }

    return headerKeyIndexMap
  }, {} as HeaderKeyIndexMap)
